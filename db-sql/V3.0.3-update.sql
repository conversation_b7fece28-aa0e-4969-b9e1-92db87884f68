-- 最后时间不对的问题，更新 SQL
-- 每个表都需要执行到
UPDATE
    msg_last a,
    (
        SELECT
            relation,
            max(msg_timestamp) as msg_timestamp
        from
            msg_info_202309
        group by
            relation) c
set
    a.msg_timestamp = c.msg_timestamp
where
        a.relation = c.relation
  and c.msg_timestamp > a.msg_timestamp
  -- 更新 9 月份的数据，就使用 10 月初，
  -- 更新 9 月份的数据，就使用 9 月初，依次类推
  -- 此条件不加也可以
  and a.msg_timestamp <= UNIX_TIMESTAMP('2023-10-01 00:00:00') * 1000;

-- 开始时间不对的问题更新 SQL
update msg_last a,user_customer_relationship b
set a.create_datetime = b.createtime - INTERVAL 1 HOUR
where a.customer_id = b.customer_id and a.user_id = b.user_id ;