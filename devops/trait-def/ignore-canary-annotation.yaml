apiVersion: oam.coding.net/v1alpha1
kind: Trait
metadata:
  name: ignore-canary-annotation
  annotations:
    name: 禁用灰度
spec:
  description: 启用该插件的服务将不会在部署策略中被部署到灰度环境
  available: true
  strategy: PATCH
  targetGVK:
    apiVersion: apps/v1
    kind: All
  contextVariables:
  - name: namespace
    description: 目标对象命名空间
    required: true
    type: STRING
  - name: src
    description: 目标对象
    required: true
    type: STRING
  - name: name
    description: 目标对象名称
    required: true
    type: STRING
  customVariables: [
    ]
  traitDefinition:
    kube:
      template: |-
        #运维插件的定义
        #假设运维插件的 id 和 name 都是 ignore-canary-annotation
        # patch 类型的插件，作用对象类型 Deployment
        metadata:
          name: {{.name}}
          namespace: {{.namespace}}
          annotations:
            oam.coding.net/ignore-canary: "true"
