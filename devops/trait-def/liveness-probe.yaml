apiVersion: oam.coding.net/v1alpha1
kind: Trait
metadata:
  name: liveness-probe
  annotations:
    name: 存活探针
spec:
  description: 指示容器是否正在运行。K8S 持续探测服务的探针，当探测失败时会重启容器
  available: true
  strategy: PATCH
  targetGVK:
    apiVersion: apps/v1
    kind: Deployment
  contextVariables:
  - name: namespace
    description: 目标对象命名空间
    required: true
    type: STRING
  - name: src
    description: 目标对象
    required: true
    type: STRING
  - name: name
    description: 目标对象名称
    required: true
    type: STRING
  customVariables:
  - name: cmd_probe
    description: K8S 通过命令对服务探活
    required: false
    type: OBJECT
  - name: failure_threadhold
    description: 当探测失败时，Kubernetes 的重试次数。默认值是 3 次
    required: false
    type: INT
  - name: http_probe
    description: K8S 通过 HTTP 接口对服务探活
    required: false
    type: OBJECT
  - name: init_delay
    description: 容器启动后要等待多少秒后才启动存活和就绪探测器，合理设置此值可避免 Pod 不断重启
    required: true
    type: INT
  - name: period_seconds
    description: 执行探测的时间间隔（单位是秒）。默认是 10 秒
    required: false
    type: INT
  - name: tcp_probe
    description: K8S 通过 tcp 对服务探活
    required: false
    type: OBJECT
  - name: timeout_seconds
    description: 探测的超时后等待多少秒。默认值是 1 秒
    required: false
    type: INT
  traitDefinition:
    kube:
      template: |-
        #运维插件的定义
        #假设运维插件的 id 和 name 都是 liveness-probe
        # patch 类型的插件，作用对象类型 Deployment
        apiVersion: apps/v1
        kind: Deployment
        metadata:
          name: {{.name}}
          namespace: {{.namespace}}
          labels:
            coding.oam.trait/liveness-probe: "true"
        spec:
          template:
            spec:
              containers:
              - name: {{.name}}
                livenessProbe:
                  {{- if .http_probe}}
                  httpGet:
                    path: {{.http_probe.path}}
                    port: {{.http_probe.port}}
                    httpHeaders:
                    {{- range .http_probe.headers}}
                    - name: {{.name}}
                      value: {{.value | quote}}
                    {{- end}}
                  {{- end}}
                  {{- if .cmd_probe}}
                  exec:
                    command:
                    {{- range .cmd_probe.cmd}}
                    - {{.}}
                    {{- end}}
                  {{- end}}
                  {{- if .tcp_probe}}
                  tcpSocket:
                    port: {{.tcp_probe.port}}
                  {{- end}}
                  initialDelaySeconds: {{.init_delay}}
                  {{- if .period_seconds}}
                  periodSeconds: {{.period_seconds}}
                  {{- end}}
                  {{- if .timeout_seconds}}
                  timeoutSeconds: {{.timeout_seconds}}
                  {{- end}}
                  {{- if .failure_threadhold}}
                  failureThreshold: {{.failure_threadhold}}
                  {{- end}}
