### GET request to example server
POST {{host}}/cy/test
Content-Type: application/x-www-form-urlencoded

functionCode = msg/info &
notesid = JiangLi  &
body = {"page_size":20,"page_num":3,"search":
{"content":"测试","exclude_content":"","msg_send_type":1,
"external":true,
"start_time":1743437368000,"end_time":1752566930485,
"scope":{"user_ids":[],"department_ids":[1]},
"msg_id":"",
"pageup":true,
"single":true,
"filter_group_msg":false,"department_ids":[1],
"user_ids":[],"yxbzLc":"","qxktCyb":""}}
###

### GET request to example server
POST {{host}}/cy/test
Content-Type: application/x-www-form-urlencoded

functionCode = msg/info/count &
notesid = JiangLi  &
body = {"content":"测试22","exclude_content":"","msg_send_type":1,
"external":true,
"start_time":1743437368000,"end_time":1752566930485,
"scope":{"user_ids":[],"department_ids":[1]},
"msg_id":"",
"pageup":true,
"single":true,
"filter_group_msg":false,"department_ids":[1],
"user_ids":[],"yxbzLc":"","qxktCyb":""}
###