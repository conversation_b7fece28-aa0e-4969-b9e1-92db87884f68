<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.guosen</groupId>
    <artifactId>ewas</artifactId>
    <version>3.10.0.0${changelist}</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <hutool.version>5.8.28</hutool.version>
        <emoji.version>5.1.1</emoji.version>
        <pagehelper.version>5.3.2</pagehelper.version>
        <fastjson.version>1.2.83</fastjson.version>
        <!-- 此包升级需要改代码 -->
        <jedis.version>2.10.2</jedis.version>
        <!-- 版本变量命名与spring boot 一致，方可覆盖spring boot 引用 -->
        <jackson-bom.version>2.15.2</jackson-bom.version>
        <!-- log4j：版本变量命名与spring boot 一致，方可覆盖spring boot 引用 -->
        <log4j2.version>2.17.1</log4j2.version>
        <commons.version>1.2.8</commons.version>
        <jave-all-deps.version>3.3.1</jave-all-deps.version>
        <jodconverter.version>4.4.6</jodconverter.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-bom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-bom.version}</version>
        </dependency>

        <!-- 国信依赖相关包 开始 -->
        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>ewas-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>zebra-core</artifactId>
            <version>4.0.10-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.guosen</groupId>-->
        <!--            <artifactId>zebra-distributed-job</artifactId>-->
        <!--            <version>4.0.10-RELEASE</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>zebra-distributed-lock</artifactId>
            <version>4.0.10-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>zebra-cache</artifactId>
            <version>4.0.10-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>zebra-database</artifactId>
            <version>4.0.10-RELEASE</version>
        </dependency>
        <!-- jxh 精细化平台 -->
        <dependency>
            <groupId>com.guosen.jxh.jxhcommonsrv</groupId>
            <artifactId>springbt-common</artifactId>
            <version>${commons.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.guosen.jxh.jxhcommonsrv</groupId>
            <artifactId>springbt-zebra</artifactId>
            <version>${commons.version}</version>
        </dependency>
        <!-- 国信依赖相关包 结束 -->
        <!-- 工具 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-dfa</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>${emoji.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>
        <!-- 生成代码插件 -->
        <!--<dependency>
            <groupId>com.chaoying</groupId>
            <artifactId>mybaits-mola</artifactId>
            <version>guosen-hub-msg-SNAPSHOT</version>
        </dependency>-->
        <!--会话拉取-->
        <dependency>
            <groupId>com.tencent</groupId>
            <artifactId>wework</artifactId>
            <scope>system</scope>
            <version>1.0</version>
            <systemPath>${basedir}/src/main/resources/libs/wework-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>3.7.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.126</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/resources/libs/aws-java-sdk-s3-1.11.126-with-dependencies.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.guosen</groupId>
            <artifactId>zebra-service</artifactId>
            <version>4.0.6-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- jave-all-deps：音频转写：此依赖库挺大，如果可以自己在服务器安装 ffmpeg 可不装载 -->
        <!-- 为了适配各种环境，此处便捷装载所有 -->
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-all-deps</artifactId>
            <version>${jave-all-deps.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>${jodconverter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-local</artifactId>
            <version>${jodconverter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>
        <!-- 国信那边版本不一致，主动设置下版本 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.0</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.27.1</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
    </dependencies>

    <!-- Package as an executable jar -->
    <build>
        <!-- 需要开发人员自己定义 -->
        <finalName>ewasService</finalName>
        <resources>
            <resource>
                <directory>${basedir}</directory>
                <filtering>true</filtering>
                <includes>
                    <include>VERSION</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.guosen</groupId>
                <artifactId>zebra-maven-plugin</artifactId>
                <version>4.0.10-RELEASE</version>
                <configuration>
                    <jarName>${project.build.finalName}</jarName>
                    <appName>${project.build.finalName}</appName>
                    <filePath>${project.basedir}</filePath>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>genShell</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/deploy/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>com.guosen</groupId>
                                        <artifactId>
                                            zebra-maven-plugin
                                        </artifactId>
                                        <versionRange>
                                            [4.0.10-RELEASE,)
                                        </versionRange>
                                        <goals>
                                            <goal>proto2java</goal>
                                            <goal>genShell</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore></ignore>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <excludes>
                            <exclude>*.*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
    <pluginRepositories>
        <pluginRepository>
            <id>serverless-100018940053-guosen-session-ewas</id>
            <url>https://serverless-100018940053-maven.pkg.coding.net/repository/guosen-session/ewas/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <!--必须与 settings.xml 的 id 一致-->
            <id>serverless-100018940053-guosen-session-ewas</id>
            <name>ewas</name>
            <url>https://serverless-100018940053-maven.pkg.coding.net/repository/guosen-session/ewas/</url>
        </repository>
    </distributionManagement>
</project>