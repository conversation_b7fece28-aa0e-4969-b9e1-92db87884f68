<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.common.dao.HealthMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.common.domain.po.Health">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="redis" jdbcType="TINYINT" property="redis"/>
        <result column="es" jdbcType="TINYINT" property="es"/>
        <result column="msg" jdbcType="TINYINT" property="msgPull"/>
        <result column="msg_db" jdbcType="TINYINT" property="msgDb"/>
        <result column="msg_es" jdbcType="TINYINT" property="msgEs"/>
        <result column="msg_state" jdbcType="TINYINT" property="msgState"/>
        <result column="msg_error" jdbcType="INTEGER" property="msgError"/>
        <result column="msg_voice" jdbcType="INTEGER" property="msgVoice"/>
        <result column="msg_oss" jdbcType="INTEGER" property="msgOss"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,redis,es,msg,msg_db,msg_es,msg_state,msg_error,msg_voice,msg_oss,create_datetime,remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from health
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from health
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.guosen.ewas.wechat.common.domain.po.Health">
        insert into health
        (redis, es, msg, msg_db, msg_es, msg_state, msg_error, msg_voice, msg_oss, create_datetime, remark)
        values (#{redis,jdbcType=TINYINT}, #{es,jdbcType=TINYINT}, #{msgPull,jdbcType=TINYINT},
        #{msgDb,jdbcType=TINYINT}, #{msgEs,
     jdbcType=TINYINT}, #{msgState,jdbcType=TINYINT}, #{msgError,jdbcType=INTEGER}, #{msgVoice,jdbcType=INTEGER}, #{msgOss,
     jdbcType=INTEGER}, #{createDatetime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.guosen.ewas.wechat.common.domain.po.Health">
        insert into health
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="redis != null">
                redis,
            </if>
            <if test="es != null">
                es,
            </if>
            <if test="msgPull != null">
                msg,
            </if>
            <if test="msgDb != null">
                msg_db,
            </if>
            <if test="msgEs != null">
                msg_es,
            </if>
            <if test="msgState != null">
                msg_state,
            </if>
            <if test="msgError != null">
                msg_error,
            </if>
            <if test="msgVoice != null">
                msg_voice,
            </if>
            <if test="msgOss != null">
                msg_oss,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="redis != null">
                #{redis,jdbcType=TINYINT},
            </if>
            <if test="es != null">
                #{es,jdbcType=TINYINT},
            </if>
            <if test="msgPull != null">
                #{msgPull,jdbcType=TINYINT},
            </if>
            <if test="msgDb != null">
                #{msgDb,jdbcType=TINYINT},
            </if>
            <if test="msgEs != null">
                #{msgEs,jdbcType=TINYINT},
            </if>
            <if test="msgState != null">
                #{msgState,jdbcType=TINYINT},
            </if>
            <if test="msgError != null">
                #{msgError,jdbcType=INTEGER},
            </if>
            <if test="msgVoice != null">
                #{msgVoice,jdbcType=INTEGER},
            </if>
            <if test="msgOss != null">
                #{msgOss,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.guosen.ewas.wechat.common.domain.po.Health">
        update health
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="redis != null">
                redis = #{redis,jdbcType=TINYINT},
            </if>
            <if test="es != null">
                es = #{es,jdbcType=TINYINT},
            </if>
            <if test="msgPull != null">
                msg = #{msgPull,jdbcType=TINYINT},
            </if>
            <if test="msgDb != null">
                msg_db = #{msgDb,jdbcType=TINYINT},
            </if>
            <if test="msgEs != null">
                msg_es = #{msgEs,jdbcType=TINYINT},
            </if>
            <if test="msgState != null">
                msg_state = #{msgState,jdbcType=TINYINT},
            </if>
            <if test="msgError != null">
                msg_error = #{msgError,jdbcType=INTEGER},
            </if>
            <if test="msgVoice != null">
                msg_voice = #{msgVoice,jdbcType=INTEGER},
            </if>
            <if test="msgOss != null">
                msg_oss = #{msgOss,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.guosen.ewas.wechat.common.domain.po.Health">
        update health
        set id = #{id,jdbcType=BIGINT},
        redis = #{redis,jdbcType=TINYINT},
        es = #{es,jdbcType=TINYINT},
        msg = #{msgPull,jdbcType=TINYINT},
        msg_db = #{msgDb,jdbcType=TINYINT},
        msg_es = #{msgEs,jdbcType=TINYINT},
        msg_state = #{msgState,jdbcType=TINYINT},
        msg_error = #{msgError,jdbcType=INTEGER},
        msg_voice = #{msgVoice,jdbcType=INTEGER},
        msg_oss = #{msgOss,jdbcType=INTEGER},
        create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectSelective" parameterType="com.guosen.ewas.wechat.common.domain.po.Health"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from health
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="redis != null">
                and redis = #{redis,jdbcType=TINYINT}
            </if>
            <if test="es != null">
                and es = #{es,jdbcType=TINYINT}
            </if>
            <if test="msgPull != null">
                and msg = #{msgPull,jdbcType=TINYINT}
            </if>
            <if test="msgDb != null">
                and msg_db = #{msgDb,jdbcType=TINYINT}
            </if>
            <if test="msgEs != null">
                and msg_es = #{msgEs,jdbcType=TINYINT}
            </if>
            <if test="msgState != null">
                and msg_state = #{msgState,jdbcType=TINYINT}
            </if>
            <if test="msgError != null">
                and msg_error = #{msgError,jdbcType=INTEGER}
            </if>
            <if test="msgVoice != null">
                and msg_voice = #{msgVoice,jdbcType=INTEGER}
            </if>
            <if test="msgOss != null">
                and msg_oss = #{msgOss,jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                and create_datetime = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectCountSelective" parameterType="com.guosen.ewas.wechat.common.domain.po.Health"
            resultType="java.lang.Long">
        select count(1) from health
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="redis != null">
                and redis = #{redis,jdbcType=TINYINT}
            </if>
            <if test="es != null">
                and es = #{es,jdbcType=TINYINT}
            </if>
            <if test="msgPull != null">
                and msg = #{msgPull,jdbcType=TINYINT}
            </if>
            <if test="msgDb != null">
                and msg_db = #{msgDb,jdbcType=TINYINT}
            </if>
            <if test="msgEs != null">
                and msg_es = #{msgEs,jdbcType=TINYINT}
            </if>
            <if test="msgState != null">
                and msg_state = #{msgState,jdbcType=TINYINT}
            </if>
            <if test="msgError != null">
                and msg_error = #{msgError,jdbcType=INTEGER}
            </if>
            <if test="msgVoice != null">
                and msg_voice = #{msgVoice,jdbcType=INTEGER}
            </if>
            <if test="msgOss != null">
                and msg_oss = #{msgOss,jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                and create_datetime = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByPrimarys" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from health
        <where>
            <if test="ids != null">
                AND id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
</mapper>