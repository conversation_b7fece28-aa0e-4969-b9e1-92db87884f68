<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.msg.dao.MsgInfoStatsMapper">
    <select id="userStats" resultType="com.guosen.ewas.wechat.msg.domain.bo.MsgUserStatsBO">
        <!-- 分组统计，然后 去重链接，可能有性能问题，需要再考虑如何优化 -->
        select user_id     as userId,
               customer_id as customerId,
               relation from (
        <foreach collection="tables" separator="UNION ALL" item="table">
            SELECT user_id,
                   customer_id,
                   relation,
                   msg_timestamp
            FROM ${table}
            WHERE customer_id IS NOT NULL
              AND user_id IS NOT NULL
            <if test="startTime != null">
                AND msg_timestamp &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND msg_timestamp &lt;= #{endTime}
            </if>
            <if test="userIds != null and userIds.size() != 0">
                AND user_id in
                <foreach collection="userIds" open="(" separator="," close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </foreach>
        ) as combined
        GROUP BY relation
        ORDER BY msg_timestamp desc
    </select>

    <select id="userMsgStats" resultType="com.guosen.ewas.wechat.msg.domain.bo.MsgUserStatsBO">
        SELECT user_id     as userId,
               customer_id as customerId,
               relation,
               send_type   as sendType,
               count(1)    as msgNum
        from (
        <foreach collection="tables" separator="UNION ALL" item="table">
            SELECT user_id,
                   customer_id,
                   relation,
                   send_type
            FROM ${table}
            WHERE customer_id IS NOT NULL
              AND recall = 0
            <if test="startTime != null">
                AND msg_timestamp &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND msg_timestamp &lt;= #{endTime}
            </if>
            <if test="relations != null and relations.size() != 0">
                AND relation in
                <foreach collection="relations" open="(" separator="," close=")" item="rel">
                    #{rel}
                </foreach>
            </if>
        </foreach>
        ) as combined
        GROUP BY relation, send_type
    </select>
</mapper>