<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.moment.dao.MomentMapper">
  <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.moment.domain.entity.Moment">
    <!--@mbg.generated-->
    <!--@Table moment-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="moment_id" jdbcType="VARCHAR" property="momentId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_type" jdbcType="TINYINT" property="createType" />
    <result column="visible_type" jdbcType="TINYINT" property="visibleType" />
    <result column="text_content" jdbcType="VARCHAR" property="textContent" />
    <result column="image_media_id" jdbcType="VARCHAR" property="imageMediaId" />
    <result column="video_media_id" jdbcType="VARCHAR" property="videoMediaId" />
    <result column="video_thumb_media_id" jdbcType="VARCHAR" property="videoThumbMediaId" />
    <result column="link_title" jdbcType="VARCHAR" property="linkTitle" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="location_latitude" jdbcType="VARCHAR" property="locationLatitude" />
    <result column="location_longitude" jdbcType="VARCHAR" property="locationLongitude" />
    <result column="location_name" jdbcType="VARCHAR" property="locationName" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, moment_id, creator, create_time, create_type, visible_type, text_content, image_media_id, 
    video_media_id, video_thumb_media_id, link_title, link_url, location_latitude, location_longitude, 
    location_name, create_datetime, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from moment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from moment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.guosen.ewas.wechat.moment.domain.entity.Moment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into moment (moment_id, creator, create_time, 
      create_type, visible_type, text_content, 
      image_media_id, video_media_id, video_thumb_media_id, 
      link_title, link_url, location_latitude, 
      location_longitude, location_name, create_datetime, 
      remark)
    values (#{momentId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{createType,jdbcType=TINYINT}, #{visibleType,jdbcType=TINYINT}, #{textContent,jdbcType=VARCHAR}, 
      #{imageMediaId,jdbcType=VARCHAR}, #{videoMediaId,jdbcType=VARCHAR}, #{videoThumbMediaId,jdbcType=VARCHAR}, 
      #{linkTitle,jdbcType=VARCHAR}, #{linkUrl,jdbcType=VARCHAR}, #{locationLatitude,jdbcType=VARCHAR}, 
      #{locationLongitude,jdbcType=VARCHAR}, #{locationName,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.guosen.ewas.wechat.moment.domain.entity.Moment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into moment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="momentId != null">
        moment_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createType != null">
        create_type,
      </if>
      <if test="visibleType != null">
        visible_type,
      </if>
      <if test="textContent != null">
        text_content,
      </if>
      <if test="imageMediaId != null">
        image_media_id,
      </if>
      <if test="videoMediaId != null">
        video_media_id,
      </if>
      <if test="videoThumbMediaId != null">
        video_thumb_media_id,
      </if>
      <if test="linkTitle != null">
        link_title,
      </if>
      <if test="linkUrl != null">
        link_url,
      </if>
      <if test="locationLatitude != null">
        location_latitude,
      </if>
      <if test="locationLongitude != null">
        location_longitude,
      </if>
      <if test="locationName != null">
        location_name,
      </if>
      <if test="createDatetime != null">
        create_datetime,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="momentId != null">
        #{momentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createType != null">
        #{createType,jdbcType=TINYINT},
      </if>
      <if test="visibleType != null">
        #{visibleType,jdbcType=TINYINT},
      </if>
      <if test="textContent != null">
        #{textContent,jdbcType=VARCHAR},
      </if>
      <if test="imageMediaId != null">
        #{imageMediaId,jdbcType=VARCHAR},
      </if>
      <if test="videoMediaId != null">
        #{videoMediaId,jdbcType=VARCHAR},
      </if>
      <if test="videoThumbMediaId != null">
        #{videoThumbMediaId,jdbcType=VARCHAR},
      </if>
      <if test="linkTitle != null">
        #{linkTitle,jdbcType=VARCHAR},
      </if>
      <if test="linkUrl != null">
        #{linkUrl,jdbcType=VARCHAR},
      </if>
      <if test="locationLatitude != null">
        #{locationLatitude,jdbcType=VARCHAR},
      </if>
      <if test="locationLongitude != null">
        #{locationLongitude,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null">
        #{locationName,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.guosen.ewas.wechat.moment.domain.entity.Moment">
    <!--@mbg.generated-->
    update moment
    <set>
      <if test="momentId != null">
        moment_id = #{momentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createType != null">
        create_type = #{createType,jdbcType=TINYINT},
      </if>
      <if test="visibleType != null">
        visible_type = #{visibleType,jdbcType=TINYINT},
      </if>
      <if test="textContent != null">
        text_content = #{textContent,jdbcType=VARCHAR},
      </if>
      <if test="imageMediaId != null">
        image_media_id = #{imageMediaId,jdbcType=VARCHAR},
      </if>
      <if test="videoMediaId != null">
        video_media_id = #{videoMediaId,jdbcType=VARCHAR},
      </if>
      <if test="videoThumbMediaId != null">
        video_thumb_media_id = #{videoThumbMediaId,jdbcType=VARCHAR},
      </if>
      <if test="linkTitle != null">
        link_title = #{linkTitle,jdbcType=VARCHAR},
      </if>
      <if test="linkUrl != null">
        link_url = #{linkUrl,jdbcType=VARCHAR},
      </if>
      <if test="locationLatitude != null">
        location_latitude = #{locationLatitude,jdbcType=VARCHAR},
      </if>
      <if test="locationLongitude != null">
        location_longitude = #{locationLongitude,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null">
        location_name = #{locationName,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.guosen.ewas.wechat.moment.domain.entity.Moment">
    <!--@mbg.generated-->
    update moment
    set moment_id = #{momentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      create_type = #{createType,jdbcType=TINYINT},
      visible_type = #{visibleType,jdbcType=TINYINT},
      text_content = #{textContent,jdbcType=VARCHAR},
      image_media_id = #{imageMediaId,jdbcType=VARCHAR},
      video_media_id = #{videoMediaId,jdbcType=VARCHAR},
      video_thumb_media_id = #{videoThumbMediaId,jdbcType=VARCHAR},
      link_title = #{linkTitle,jdbcType=VARCHAR},
      link_url = #{linkUrl,jdbcType=VARCHAR},
      location_latitude = #{locationLatitude,jdbcType=VARCHAR},
      location_longitude = #{locationLongitude,jdbcType=VARCHAR},
      location_name = #{locationName,jdbcType=VARCHAR},
      create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectOneByMomentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moment
        where moment_id = #{momentId,jdbcType=VARCHAR}
        limit 1
    </select>
    <select id="selectWaitSyncTask" resultType="java.lang.String">
        SELECT distinct m.moment_id
        FROM moment m
                 LEFT JOIN moment_task mt ON m.moment_id = mt.moment_id
        <where>
            <if test="createType != null">
                m.create_type = #{createType,jdbcType=TINYINT}
            </if>
            <if test="startTime != null">
                and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and m.create_time &lt;= #{endTime,jdbcType=BIGINT}
            </if>
            <if test="true">
                AND (mt.publish_status = 0 OR mt.id IS NULL)
            </if>
        </where>
    </select>
    <select id="selectPersonalWaitSyncVisible" resultMap="BaseResultMap">
        select m.id, m.moment_id, m.creator
        from moment m
                 left join moment_visible_stats mv on
            m.moment_id = mv.moment_id
        where m.create_type = 1
        <if test="startTime != null">
            and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
        </if>
        and mv.id is null;
    </select>
    <select id="selectEnterpriseWaitSyncVisible" resultMap="BaseResultMap">
        select DISTINCT m.id,
                        m.moment_id,
                        m.create_type,
                        mt.userid as creator
        from moment m
                 left join moment_task mt on m.moment_id = mt.moment_id and mt.publish_status = 1
                 left join moment_visible_stats mv on m.moment_id = mv.moment_id and mv.userid = mt.userid
        where m.create_type = 0
          and mt.userid is not null
        <if test="startTime != null">
            and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
        </if>
        and mv.id is null
    </select>
    <select id="selectPersonalWaitSyncComments" resultMap="BaseResultMap">
        select m.id, m.moment_id, m.creator
        from moment m
        where m.create_type = 1
        <if test="startTime != null">
            and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            and m.create_time &lt;= #{endTime,jdbcType=BIGINT}
        </if>
    </select>
    <select id="selectEnterpriseWaitSyncComments" resultMap="BaseResultMap">
        SELECT DISTINCT m.id,
                        m.moment_id,
                        m.create_type,
                        mt.userid as creator
        FROM moment m
                 LEFT JOIN moment_task mt ON m.moment_id = mt.moment_id
        where m.create_type = 0
          and mt.publish_status = 1
        <if test="startTime != null">
            and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            and m.create_time &lt;= #{endTime,jdbcType=BIGINT}
        </if>
    </select>
    <select id="selectByMomentBO" parameterType="com.guosen.ewas.wechat.moment.domain.MomentBO" resultType="com.guosen.ewas.wechat.moment.domain.MomentBO">
        select m.id,
               m.moment_id            as momentId,
               m.creator,
               m.create_time          as createTime,
               m.create_type          as createType,
               m.visible_type         as visibleType,
               m.text_content         as textContent,
               m.image_media_id       as imageMediaId,
               m.video_media_id       as videoMediaId,
               m.video_thumb_media_id as videoThumbMediaId,
               m.link_title           as linkTitle,
               m.link_url             as linkUrl,
               m.location_latitude    as locationLatitude,
               m.location_longitude   as locationLongitude,
               m.location_name        as locationName,
               m.create_datetime      as createDatetime,
               m.remark,
               u.name                 as userName,
               u.id                   as userId,
               u.avatar               as userAvatar
        from moment m
                 left join user u on m.creator = u.userid
        <where>
            <if test="textContent != null and textContent != ''">
                m.text_content like concat('%', #{textContent,jdbcType=VARCHAR}, '%')
            </if>
            <if test="startTime != null">
                and m.create_time &gt;= #{startTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and m.create_time &lt;= #{endTime,jdbcType=BIGINT}
            </if>
            <if test="createType != null">
                and m.create_type = #{createType,jdbcType=TINYINT}
            </if>
            <if test="userIds != null and userIds.size() &gt; 0">
                and u.id in
                <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
                    #{userId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
</mapper>