<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.guosen.ewas.wechat.customer.dao.GroupChatMapper">
    <resultMap type="com.guosen.ewas.wechat.customer.domain.entity.GroupChat" id="GroupChatMap">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="chatId" column="chat_id"/>
        <result property="name" column="name"/>
        <result property="ownerId" column="owner_id"/>
        <result property="createTime" column="create_time"/>
        <result property="notice" column="notice"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyDatetime" column="modify_datetime"/>
        <result property="groupType" column="group_type"/>
        <result property="isDissolution" column="is_dissolution"/>
        <result property="lastMsgTime" column="last_msg_time"/>
    </resultMap>

    <sql id="selectGroupChatVo">
        id
                ,
        status,
        chat_id,
        name,
        owner_id,
        create_time,
        notice,
        modifier,
        modify_datetime,
        group_type,
        is_dissolution,
        last_msg_time
    </sql>

    <select id="selectById" parameterType="Long" resultMap="GroupChatMap">
        SELECT
        <include refid="selectGroupChatVo"/>
        FROM group_chat
        WHERE id = #{id}
    </select>

    <select id="selectByChatId" resultMap="GroupChatMap">
        SELECT
        <include refid="selectGroupChatVo"/>
        FROM group_chat
        WHERE chat_id = #{chatId}
    </select>

    <insert id="insert" parameterType="com.guosen.ewas.wechat.customer.domain.entity.GroupChat" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO group_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">
                status,
            </if>
            <if test="chatId != null and chatId != ''">
                chat_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="notice != null">
                notice,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="modifyDatetime != null">
                modify_datetime,
            </if>
            <if test="groupType != null">
                group_type,
            </if>
            <if test="isDissolution != null">
                is_dissolution,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="status != null">
                #{status},
            </if>
            <if test="chatId != null and chatId != ''">
                #{chatId},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="ownerId != null">
                #{ownerId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="notice != null">
                #{notice},
            </if>
            <if test="modifier != null">
                #{modifier},
            </if>
            <if test="modifyDatetime != null">
                #{modifyDatetime},
            </if>
            <if test="groupType != null">
                #{groupType},
            </if>
            <if test="isDissolution != null">
                #{isDissolution},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="com.guosen.ewas.wechat.customer.domain.entity.GroupChat"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO group_chat
        (status,
         chat_id,
         name,
         owner_id,
         create_time,
         notice,
         modifier,
         modify_datetime,
         group_type,
         is_dissolution)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.status},
             #{item.chatId},
             #{item.name},
             #{item.ownerId},
             #{item.createTime},
             #{item.notice},
             #{item.modifier},
             #{item.modifyDatetime},
             #{item.groupType},
             #{item.isDissolution})
        </foreach>
    </insert>

    <update id="update" parameterType="com.guosen.ewas.wechat.customer.domain.entity.GroupChat">
        UPDATE group_chat
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">
                status = #{status},
            </if>
            <if test="chatId != null and chatId != ''">
                chat_id = #{chatId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="notice != null">
                notice = #{notice},
            </if>
            <if test="modifier != null">
                modifier = #{modifier},
            </if>
            <if test="modifyDatetime != null">
                modify_datetime = #{modifyDatetime},
            </if>
            <if test="groupType != null">
                group_type = #{groupType},
            </if>
            <if test="isDissolution != null">
                is_dissolution = #{isDissolution},
            </if>
        </trim>
        WHERE id = #{id}
    </update>

    <select id="selectMemberCountLgTwoHCount" resultType="java.lang.Integer">
        SELECT IFNULL(sum(gcm.id), 0) number,
               gcm.group_id AS        group_id
        FROM group_chat gc,
             group_chat_member gcm
        WHERE gcm.group_id = gc.id
        GROUP BY gcm.group_id
    </select>

    <select id="selectStatisticsByGroup"
            resultType="com.guosen.ewas.wechat.customer.domain.bo.GroupStatisticsBO">
        SELECT gc.id             AS groupId,
               COUNT(gcm.id)     AS userCount,
               SUM(gcm.type = 2) AS customerCount
        FROM group_chat gc
                 LEFT JOIN group_chat_member gcm ON gc.id = gcm.group_id
        where gc.group_type = 1
        GROUP BY gc.id
    </select>

    <select id="selectNeedSyncGroupOwnerids" resultType="java.lang.String">
        SELECT DISTINCT (u.userid) AS ownerUserid
        FROM group_chat gc
                 LEFT JOIN user u ON gc.owner_id = u.id
        WHERE gc.is_dissolution = 0
          AND u.is_delete = 0
    </select>

    <select id="selectGroupByMemberUser" resultType="com.guosen.ewas.wechat.customer.domain.vo.LastMsgGroupVO">
        SELECT DISTINCT gc.id            AS id,
                        gc.name          AS groupName,
                        gc.chat_id       AS chatid,
                        gc.last_msg_time as lastMsgTime,
                        gc.last_msg      as lastMsgContent
        FROM group_chat gc
                 LEFT JOIN group_chat_member gcm ON gc.id = gcm.group_id
                 LEFT JOIN user u ON u.userid = gcm.member_userid and gcm.type = 1
        <where>
            <if test="name != null and name != ''">
                AND gc.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="userId != null">
                AND u.id = #{userId}
            </if>
        </where>
        order by gc.last_msg_time desc
    </select>

    <select id="selectGroupByMemberCustomer" resultType="com.guosen.ewas.wechat.customer.domain.vo.LastMsgGroupVO">
        SELECT DISTINCT gc.id            AS id,
                        gc.name          AS groupName,
                        gc.chat_id       AS chatid,
                        gc.last_msg_time as lastMsgTime,
                        gc.last_msg      as lastMsgContent
        FROM group_chat gc
                 LEFT JOIN group_chat_member gcm ON gc.id = gcm.group_id
                 LEFT JOIN customer c ON c.external_userid = gcm.member_userid AND gcm.type = 2
        <where>
            gc.is_dissolution = 0
            <if test="name != null and name != ''">
                AND gc.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="customerId != null">
                AND c.id = #{customerId}
            </if>
            order by gc.last_msg_time desc
        </where>
    </select>

    <select id="selectInfoByGroupId" resultType="com.guosen.ewas.wechat.common.domain.GroupInfo">
        SELECT id      AS id,
               name    AS groupName,
               chat_id AS chatid
        FROM group_chat
        WHERE id = #{groupId}
    </select>

    <select id="selectInfoByChatId" resultType="com.guosen.ewas.wechat.common.domain.GroupInfo">
        SELECT id      AS id,
               name    AS groupName,
               chat_id AS chatid
        FROM group_chat
        WHERE chat_id = #{chatId}
    </select>

    <select id="checkGroupScope" resultType="java.lang.Integer">
        SELECT count(gcm.id)
        FROM group_chat gc
                 LEFT JOIN group_chat_member gcm ON gc.id = gcm.group_id
        <where>
            gc.is_dissolution = 0
              AND gc.group_type = 1
            <if test="chatid != null and chatid != ''">
                AND gc.chat_id = #{chatid}
            </if>
            <if test="userid != null and userid != ''">
                AND gcm.member_userid = #{userid}
            </if>
            <if test="type != null">
                AND gcm.type = #{type}
            </if>
        </where>
    </select>

    <update id="updateLastMsg">
        <foreach collection="bos" separator=";" item="item">
            UPDATE group_chat
            SET last_msg      = #{item.lastMsg},
                last_msg_time = #{item.lastMsgTime}
            WHERE id = #{item.groupId}
              AND is_dissolution = 0
        </foreach>
        ;
    </update>

    <select id="selectByOwnerIdList" resultType="java.lang.Long">
        SELECT id
        FROM group_chat
        WHERE is_dissolution = 0
        <if test="userIds != null and userIds.size() != 0">
            AND owner_id in (
            <foreach collection="userIds" item="userId" separator=",">
                #{userId}
            </foreach>
            )
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-08-21-->
    <select id="selectByIdIn" resultMap="GroupChatMap">
        select
        <include refid="selectGroupChatVo"/>
        from group_chat
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.customer.domain.entity.GroupChat" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into group_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="modifyDatetime != null">
                modify_datetime,
            </if>
            <if test="chatId != null">
                chat_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="notice != null">
                notice,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="groupType != null">
                group_type,
            </if>
            <if test="memberCount != null">
                member_count,
            </if>
            <if test="isDissolution != null">
                is_dissolution,
            </if>
            <if test="lastMsgTime != null">
                last_msg_time,
            </if>
            <if test="lastMsg != null">
                last_msg,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="modifyDatetime != null">
                #{modifyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="chatId != null">
                #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="notice != null">
                #{notice,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="groupType != null">
                #{groupType,jdbcType=BOOLEAN},
            </if>
            <if test="memberCount != null">
                #{memberCount,jdbcType=INTEGER},
            </if>
            <if test="isDissolution != null">
                #{isDissolution,jdbcType=BOOLEAN},
            </if>
            <if test="lastMsgTime != null">
                #{lastMsgTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastMsg != null">
                #{lastMsg,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="modifyDatetime != null">
                modify_datetime = #{modifyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="chatId != null">
                chat_id = #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="notice != null">
                notice = #{notice,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=BOOLEAN},
            </if>
            <if test="memberCount != null">
                member_count = #{memberCount,jdbcType=INTEGER},
            </if>
            <if test="isDissolution != null">
                is_dissolution = #{isDissolution,jdbcType=BOOLEAN},
            </if>
            <if test="lastMsgTime != null">
                last_msg_time = #{lastMsgTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastMsg != null">
                last_msg = #{lastMsg,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2023-12-08-->
    <select id="selectByAll" resultMap="GroupChatMap">
        select
        <include refid="selectGroupChatVo"/>
        from group_chat
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="status != null">
                and `status` = #{status}
            </if>
            <if test="chatId != null">
                and chat_id = #{chatId}
            </if>
            <if test="name != null">
                and `name` like concat(#{name}, '%')
            </if>
            <if test="ownerId != null">
                and owner_id = #{ownerId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="notice != null">
                and notice = #{notice}
            </if>
            <if test="modifier != null">
                and modifier = #{modifier}
            </if>
            <if test="modifyDatetime != null">
                and modify_datetime = #{modifyDatetime}
            </if>
            <if test="groupType != null">
                and group_type = #{groupType}
            </if>
            <if test="isDissolution != null">
                and is_dissolution = #{isDissolution}
            </if>
            <if test="lastMsgTime != null">
                and last_msg_time = #{lastMsgTime}
            </if>
        </where>
    </select>
</mapper>