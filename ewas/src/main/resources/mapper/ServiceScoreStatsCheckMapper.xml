<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.score.dao.ServiceScoreStatsCheckMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.score.domain.entity.ServiceScoreStatsCheck">
        <!--@mbg.generated-->
        <!--@Table service_score_stats_check-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="check_id" jdbcType="BIGINT" property="checkId"/>
        <result column="check_date" jdbcType="DATE" property="checkDate"/>
        <result column="check_num" jdbcType="BIGINT" property="checkNum"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="update" jdbcType="TIMESTAMP" property="update"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        check_id,
        check_date,
        check_num,
        remark,
        `update`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from service_score_stats_check
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from service_score_stats_check
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.score.domain.entity.ServiceScoreStatsCheck" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into service_score_stats_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkId != null">
                check_id,
            </if>
            <if test="checkDate != null">
                check_date,
            </if>
            <if test="checkNum != null">
                check_num,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="update != null">
                `update`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkId != null">
                #{checkId,jdbcType=BIGINT},
            </if>
            <if test="checkDate != null">
                #{checkDate,jdbcType=DATE},
            </if>
            <if test="checkNum != null">
                #{checkNum,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="update != null">
                #{update,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.guosen.ewas.wechat.score.domain.entity.ServiceScoreStatsCheck">
        <!--@mbg.generated-->
        update service_score_stats_check
        <set>
            <if test="checkId != null">
                check_id = #{checkId,jdbcType=BIGINT},
            </if>
            <if test="checkDate != null">
                check_date = #{checkDate,jdbcType=DATE},
            </if>
            <if test="checkNum != null">
                check_num = #{checkNum,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="update != null">
                `update` = #{update,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2023-12-21-->
    <select id="selectByAll" resultType="com.guosen.ewas.wechat.score.domain.vo.ServiceScoreStatsCheckVO">
        select ss.check_id  as checkId,
               ss.check_num as checkNum,
               u.userid     as checkUserid,
               u.name       as checkName
        from (SELECT check_id,
                     sum(check_num) as check_num
              from service_score_stats_check
        <where>
            <if test="scope != null and scope.userIds != null and scope.userIds.size() != 0">
                and check_id in
                <foreach collection="scope.userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="checkId != null">
                and check_id = #{checkId,jdbcType=BIGINT}
            </if>
            <if test="checkDate != null">
                and check_date = #{checkDate,jdbcType=DATE}
            </if>
            <if test="checkNum != null">
                and check_num = #{checkNum,jdbcType=BIGINT}
            </if>
            <if test="startDate != null">
                and check_date &gt;= #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null">
                and check_date &lt;= #{endDate,jdbcType=DATE}
            </if>
        </where>
        group by check_id) ss
            LEFT join `user` u on
            u.id = ss.check_id
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-12-21-->
    <delete id="deleteByCheckDate">
        delete
        from service_score_stats_check
        where check_date = #{checkDate,jdbcType=DATE}
    </delete>

    <insert id="insertStats">
        INSERT INTO service_score_stats_check
            (check_id, check_date, check_num)
        SELECT check_id,
               STR_TO_DATE(CONCAT(DATE_FORMAT(check_datetime, '%Y-%m'), '-01'), '%Y-%m-%d') AS stats_date,
               count(1)                                                                     as check_num
        from service_score_log
        where check_datetime &gt;= #{checkDate,jdbcType=DATE}
        group by check_id,
                 DATE_FORMAT(check_datetime, '%y-%m');
    </insert>
</mapper>