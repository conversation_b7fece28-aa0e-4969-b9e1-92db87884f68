<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.guosen.ewas.wechat.msg.dao.MsgFileMapper">
    <resultMap type="com.guosen.ewas.wechat.msg.domain.entity.MsgFile" id="FileInfoMap">
        <result property="id" column="id"/>
        <result property="uid" column="uid"/>
        <result property="fileName" column="file_name"/>
        <result property="saveName" column="save_name"/>
        <result property="suffix" column="suffix"/>
        <result property="fileType" column="file_type"/>
        <result property="url" column="url"/>
        <result property="path" column="path"/>
        <result property="contentType" column="content_type"/>
        <result property="msgid" column="msgid"/>
        <result property="msgseq" column="msgseq"/>
        <result property="isSync" column="is_sync"/>
        <result property="fileSize" column="file_size"/>
        <result property="sdkFileId" column="sdk_file_id"/>
        <result property="md5Sum" column="md5_sum"/>
        <result property="syncDatetime" column="sync_datetime"/>
        <result property="createDatetime" column="create_datetime"/>
        <result property="msgType" column="msg_type"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectFileInfoVo">
        id,
        uid,
        file_name,
        save_name,
        suffix,
        file_type,
        url,
        path,
        content_type,
        msgid,
        msgseq,
        is_sync,
        file_size,
        sdk_file_id,
        md5_sum,
        sync_datetime,
        create_datetime,
        msg_type,
        remark
    </sql>

    <select id="selectById" parameterType="Long" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        WHERE id = #{id}
    </select>

    <insert id="insert" parameterType="com.guosen.ewas.wechat.msg.domain.entity.MsgFile" useGeneratedKeys="true"
            keyProperty="item.id">
        INSERT INTO msg_file
        (uid,
         file_name,
         save_name,
         suffix,
         file_type,
         url,
         path,
         content_type,
         msgid,
         msgseq,
         is_sync,
         file_size,
         sdk_file_id,
         md5_sum,
         sync_datetime,
         create_datetime,
         msg_type,
         remark)
        VALUES (#{item.uid},
                #{item.fileName},
                #{item.saveName},
                #{item.suffix},
                #{item.fileType},
                #{item.url},
                #{item.path},
                #{item.contentType},
                #{item.msgid},
                #{item.msgseq},
                #{item.isSync},
                #{item.fileSize},
                #{item.sdkFileId},
                #{item.md5Sum},
                #{item.syncDatetime},
                #{item.createDatetime},
                #{item.msgType},
                #{item.remark})
    </insert>

    <insert id="insertBatch" parameterType="com.guosen.ewas.wechat.msg.domain.entity.MsgFile"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO msg_file
        (uid,
         file_name,
         save_name,
         suffix,
         file_type,
         url,
         path,
         content_type,
         msgid,
         msgseq,
         is_sync,
         file_size,
         sdk_file_id,
         md5_sum,
         sync_datetime,
         create_datetime,
         msg_type,
         remark)
        VALUES
        <foreach collection="fileList" item="item" index="index" separator=",">
            (#{item.uid},
             #{item.fileName},
             #{item.saveName},
             #{item.suffix},
             #{item.fileType},
             #{item.url},
             #{item.path},
             #{item.contentType},
             #{item.msgid},
             #{item.msgseq},
             #{item.isSync},
             #{item.fileSize},
             #{item.sdkFileId},
             #{item.md5Sum},
             #{item.syncDatetime},
             #{item.createDatetime},
             #{item.msgType},
             #{item.remark})
        </foreach>
    </insert>

    <update id="updateByUidSync">
        UPDATE msg_file
        SET is_sync       = #{state},
            sync_datetime = #{syncDatetime}
        WHERE uid = #{uid,jdbcType=VARCHAR}
    </update>

    <select id="selectByMsgId" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        WHERE msgid = #{msgid,jdbcType=VARCHAR}
    </select>

    <update id="updateByUidSelective" parameterType="com.guosen.ewas.wechat.msg.domain.entity.MsgFile">
        update msg_file
        <set>
            <if test="record.fileName != null">
                file_name = #{record.fileName,jdbcType=VARCHAR},
            </if>
            <if test="record.msgid != null">
                msgid = #{record.msgid,jdbcType=VARCHAR},
            </if>
            <if test="record.saveName != null">
                save_name = #{record.saveName,jdbcType=VARCHAR},
            </if>
            <if test="record.suffix != null">
                suffix = #{record.suffix,jdbcType=VARCHAR},
            </if>
            <if test="record.fileType != null">
                file_type = #{record.fileType,jdbcType=VARCHAR},
            </if>
            <if test="record.url != null">
                url = #{record.url,jdbcType=VARCHAR},
            </if>
            <if test="record.path != null">
                path = #{record.path,jdbcType=VARCHAR},
            </if>
            <if test="record.contentType != null">
                content_type = #{record.contentType,jdbcType=VARCHAR},
            </if>
            <if test="record.msgseq != null">
                msgseq = #{record.msgseq,jdbcType=BIGINT},
            </if>
            <if test="record.isSync != null">
                is_sync = #{record.isSync,jdbcType=TINYINT},
            </if>
            <if test="record.fileSize != null">
                file_size = #{record.fileSize,jdbcType=BIGINT},
            </if>
            <if test="record.sdkFileId != null">
                sdk_file_id = #{record.sdkFileId,jdbcType=VARCHAR},
            </if>
            <if test="record.md5Sum != null and record.md5Sum != ''">
                md5_sum = #{record.md5Sum,jdbcType=VARCHAR},
            </if>
            <if test="record.syncDatetime != null">
                sync_datetime = #{record.syncDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createDatetime != null">
                create_datetime = #{record.createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.msgType != null">
                msg_type = #{record.msgType,jdbcType=INTEGER},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where uid = #{record.uid,jdbcType=BIGINT}
    </update>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS ${tableName}
        (
            `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
            `uid`             varchar(100) NOT NULL COMMENT '唯一编码值',
            `file_name`       varchar(128) NOT NULL COMMENT '原文件名',
            `save_name`       varchar(100) NOT NULL COMMENT '文件存储名',
            `suffix`          varchar(20)  NOT NULL COMMENT '文件后缀',
            `file_type`       varchar(20)  NOT NULL COMMENT '文件类型',
            `url`             varchar(255)                                                  DEFAULT NULL COMMENT '文件url',
            `path`            varchar(500)                                                  DEFAULT NULL COMMENT '文件存储相对路径',
            `content_type`    varchar(500)                                                  DEFAULT NULL COMMENT 'HTTP内容类型',
            `msgid`           varchar(255)                                                  DEFAULT NULL COMMENT '消息的msgid',
            `msgseq`          bigint                                                        DEFAULT NULL COMMENT '消息的seq',
            `sdk_file_id`     text COMMENT '拉取文件需要的sdkid',
            `md5_sum`         varchar(100)                                                  DEFAULT NULL COMMENT '文件MD5值',
            `file_size`       bigint                                                        DEFAULT NULL COMMENT '文件大小（单位B）',
            `msg_type`        tinyint                                                       DEFAULT NULL COMMENT '消息类型：MessageEnum.code',
            `is_sync`         tinyint(1)   NOT NULL COMMENT '文件存储状态：-1-数据初始化,0-微信下载中 ,1-微信下载失败 ,2-文件存储失败 ,3-oss转存本地失败 ,7-文件存储成功 ,8-文件转存oss失败 ,9-文件转存oss成功',
            `sync_datetime`   datetime                                                      DEFAULT NULL COMMENT '同步时间',
            `create_datetime` datetime     NOT NULL                                         DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `remark`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE KEY `idx_uni_uid` (`uid`) USING BTREE,
            KEY `idx_msgid` (`msgid`) USING BTREE,
            KEY `idx_save_name` (`save_name`) USING BTREE,
            KEY `idx_create_datetime` (`create_datetime`) USING BTREE
        ) ENGINE = InnoDB
          AUTO_INCREMENT = ${idStart}
          DEFAULT CHARSET = utf8mb4 COMMENT ='消息中的文件信息(媒体消息)，包含各种：图片，表情，视频，音频，文件等信息';
    </update>

    <select id="selectByUid" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        WHERE uid = #{uid,jdbcType=VARCHAR}
    </select>

    <select id="selectByDateAndState" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM msg_file
        <where>
            <if test="startTime != null and endTime != null">
                and create_datetime BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="saveName != null and saveName != ''">
                and save_name = #{saveName}
            </if>
            <if test="states != null and states.size() > 0">
                AND is_sync IN
                <foreach collection="states" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBySaveNameAndStates" resultType="com.guosen.ewas.wechat.msg.domain.entity.MsgFile">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        <where>
            <if test="saveName != null and saveName != ''">
                and save_name = #{saveName}
            </if>
            <if test="states != null and states.size() > 0">
                AND is_sync IN
                <foreach collection="states" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
        </where>
        limit 1
    </select>

    <select id="getMaxId" resultType="long">
        SELECT max(id)
        FROM ${tableName}
    </select>

    <select id="getByMsgIdAndSdkFileId" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        WHERE msgid = #{msgid,jdbcType=VARCHAR}
          and sdk_file_id = #{sdkFileId,jdbcType=VARCHAR}
    </select>

    <update id="updateByMd5Selective">
        update msg_file
        <set>
            <if test="record.fileName != null">
                file_name = #{record.fileName,jdbcType=VARCHAR},
            </if>
            <if test="record.saveName != null">
                save_name = #{record.saveName,jdbcType=VARCHAR},
            </if>
            <if test="record.suffix != null">
                suffix = #{record.suffix,jdbcType=VARCHAR},
            </if>
            <if test="record.fileType != null">
                file_type = #{record.fileType,jdbcType=VARCHAR},
            </if>
            <if test="record.url != null">
                url = #{record.url,jdbcType=VARCHAR},
            </if>
            <if test="record.path != null">
                path = #{record.path,jdbcType=VARCHAR},
            </if>
            <if test="record.contentType != null">
                content_type = #{record.contentType,jdbcType=VARCHAR},
            </if>
            <if test="record.isSync != null">
                is_sync = #{record.isSync,jdbcType=TINYINT},
            </if>
            <if test="record.fileSize != null">
                file_size = #{record.fileSize,jdbcType=BIGINT},
            </if>
            <if test="record.syncDatetime != null">
                sync_datetime = #{record.syncDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createDatetime != null">
                create_datetime = #{record.createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.msgType != null">
                msg_type = #{record.msgType,jdbcType=INTEGER},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where md5_sum = #{record.md5Sum,jdbcType=VARCHAR}
    </update>

    <select id="getByMd5" resultMap="FileInfoMap">
        SELECT
        <include refid="selectFileInfoVo"/>
        FROM ${tableName}
        WHERE md5_sum = #{md5sum,jdbcType=VARCHAR}
        ORDER BY is_sync desc
        limit 1
    </select>

    <select id="countDownloadByCreateDateTime" resultType="java.lang.Integer">
        SELECT count(id)
        FROM msg_file
        where is_sync &lt; 7
        <if test="startTime != null and endTime != null">
            and create_datetime BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>
</mapper>