<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.uc.dao.MenuRelNodeMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.uc.domain.po.MenuRelNode">
        <!--@mbg.generated-->
        <!--@Table menu_rel_node-->
        <id column="id" property="id"/>
        <result column="menu_id" property="menuId"/>
        <result column="node_id" property="nodeId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, menu_id, node_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from menu_rel_node
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from menu_rel_node
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.guosen.ewas.wechat.uc.domain.po.MenuRelNode"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into menu_rel_node (menu_id, node_id)
        values (#{menuId}, #{nodeId})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.uc.domain.po.MenuRelNode" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into menu_rel_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null">
                menu_id,
            </if>
            <if test="nodeId != null">
                node_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null">
                #{menuId},
            </if>
            <if test="nodeId != null">
                #{nodeId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.guosen.ewas.wechat.uc.domain.po.MenuRelNode">
        <!--@mbg.generated-->
        update menu_rel_node
        <set>
            <if test="menuId != null">
                menu_id = #{menuId},
            </if>
            <if test="nodeId != null">
                node_id = #{nodeId},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.guosen.ewas.wechat.uc.domain.po.MenuRelNode">
        <!--@mbg.generated-->
        update menu_rel_node
        set menu_id = #{menuId},
        node_id = #{nodeId}
        where id = #{id}
    </update>

    <delete id="deleteByNodeId">
        delete
        from menu_rel_node
        where node_id = #{nodeId}
    </delete>

    <select id="selectMenuIdByNodeId" resultType="java.lang.Long">
        select m.menu_id
        from menu_rel_node m
        where m.node_id = #{nodeId}
    </select>

    <select id="selectByMenuIdAndNodeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from menu_rel_node
        where menu_id = #{menuId}
        and node_id = #{nodeId}
    </select>

    <select id="selectByNodeIds" resultType="com.guosen.ewas.wechat.uc.domain.po.Menu">
        select m.id, m.menu_code as menuCode, m.menu_name as menuName
        from menu m
        left join menu_rel_node mrn on m.id = mrn.menu_id
        <where>
            <if test="nodeIds != null and nodeIds.size() != 0">
                and mrn.node_id in
                <foreach collection="nodeIds" item="nodeId" open="(" separator="," close=")">
                    #{nodeId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>