<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.guosen.ewas.wechat.sensitive.dao.SensitiveTriggerLogMapper">
    <resultMap type="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerLog" id="SensitiveTriggerLogMap">
        <result property="id" column="id"/>
        <result property="sensitiveRuleId" column="sensitive_rule_id"/>
        <result property="ruleItemId" column="rule_item_id"/>
        <result property="wordsId" column="words_id"/>
        <result property="words" column="words"/>
        <result property="msgId" column="msg_id"/>
        <result property="msgType" column="msg_type"/>
        <result property="triggerId" column="trigger_id"/>
        <result property="triggerType" column="trigger_type"/>
        <result property="triggerTime" column="trigger_time"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="selectSensitiveTriggerLogVo">
        id,
        sensitive_rule_id,
        rule_item_id,
        words_id,
        words,
        msg_id,
        msg_type,
        trigger_id,
        trigger_type,
        trigger_time,
        user_id,
        create_time,
        group_id
    </sql>

    <select id="selectSelective" parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerLog"
            resultMap="SensitiveTriggerLogMap">
        SELECT
        <include refid="selectSensitiveTriggerLogVo"/>
        FROM sensitive_trigger_log
        <where>
            <if test="sensitiveRuleId != null">
                AND sensitive_rule_id = #{sensitiveRuleId}
            </if>
            <if test="ruleItemId != null">
                AND rule_item_id = #{ruleItemId}
            </if>
            <if test="wordsId != null">
                AND words_id = #{wordsId}
            </if>
            <if test="words != null">
                AND words = #{words}
            </if>
            <if test="msgId != null">
                AND msg_id = #{msgId}
            </if>
            <if test="msgType != null">
                AND msg_type = #{msgType}
            </if>
            <if test="triggerId != null">
                AND trigger_id = #{triggerId}
            </if>
            <if test="triggerType != null">
                AND trigger_type = #{triggerType}
            </if>
            <if test="triggerTime != null">
                AND trigger_time &lt;= #{triggerTime}
            </if>
            <if test="userId != null">
                AND user_id &lt;= #{userId}
            </if>
            <if test="createTime != null">
                AND create_time &lt;= #{createTime}
            </if>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
        </where>
    </select>

    <select id="selectById" parameterType="Long" resultMap="SensitiveTriggerLogMap">
        SELECT
        <include refid="selectSensitiveTriggerLogVo"/>
        FROM sensitive_trigger_log
        WHERE id = #{id}
    </select>

    <insert id="insert" parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerLog"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO sensitive_trigger_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sensitiveRuleId != null">
                sensitive_rule_id,
            </if>
            <if test="ruleItemId != null">
                rule_item_id,
            </if>
            <if test="wordsId != null">
                words_id,
            </if>
            <if test="words != null and words != ''">
                words,
            </if>
            <if test="msgId != null and msgId != ''">
                msg_id,
            </if>
            <if test="msgType != null and msgType != ''">
                msg_type,
            </if>
            <if test="triggerId != null">
                trigger_id,
            </if>
            <if test="triggerType != null">
                trigger_type,
            </if>
            <if test="triggerTime != null">
                trigger_time,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="sensitiveRuleId != null">
                #{sensitiveRuleId},
            </if>
            <if test="ruleItemId != null">
                #{ruleItemId},
            </if>
            <if test="wordsId != null">
                #{wordsId},
            </if>
            <if test="words != null and words != ''">
                #{words},
            </if>
            <if test="msgId != null and msgId != ''">
                #{msgId},
            </if>
            <if test="msgType != null and msgType != ''">
                #{msgType},
            </if>
            <if test="triggerId != null">
                #{triggerId},
            </if>
            <if test="triggerType != null">
                #{triggerType},
            </if>
            <if test="triggerTime != null">
                #{triggerTime},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="groupId != null">
                #{groupId},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerLog"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO sensitive_trigger_log
        (sensitive_rule_id,
         rule_item_id,
         words_id,
         words,
         msg_id,
         msg_type,
         trigger_id,
         trigger_type,
         trigger_time,
         user_id,
         create_time, group_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sensitiveRuleId},
             #{item.ruleItemId},
             #{item.wordsId},
             #{item.words},
             #{item.msgId},
             #{item.msgType},
             #{item.triggerId},
             #{item.triggerType},
             #{item.triggerTime},
             #{item.userId},
             #{item.createTime}, #{item.groupId})
        </foreach>
    </insert>

    <update id="update" parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerLog">
        UPDATE sensitive_trigger_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="sensitiveRuleId != null">
                sensitive_rule_id = #{sensitiveRuleId},
            </if>
            <if test="ruleItemId != null">
                rule_item_id = #{ruleItemId},
            </if>
            <if test="wordsId != null">
                words_id = #{wordsId},
            </if>
            <if test="words != null and words != ''">
                words = #{words},
            </if>
            <if test="msgId != null and msgId != ''">
                msg_id = #{msgId},
            </if>
            <if test="msgType != null">
                msg_type = #{msgType},
            </if>
            <if test="triggerId != null">
                trigger_id = #{triggerId},
            </if>
            <if test="triggerType != null">
                trigger_type = #{triggerType},
            </if>
            <if test="triggerTime != null">
                trigger_time = #{triggerTime},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
        </trim>
        WHERE id = #{id}
    </update>

    <select id="selectByDate" resultMap="SensitiveTriggerLogMap">
        SELECT
        <include refid="selectSensitiveTriggerLogVo"/>
        FROM sensitive_trigger_log
        WHERE date_format(trigger_time, '%Y-%m-%d') = #{date}
    </select>

    <select id="countTriggerLogByUserAndDate" resultType="com.guosen.ewas.wechat.sensitive.domain.vo.CountTriggerLogVO">
        SELECT count(*) AS wordsNum,
               words_id AS wordsId,
               words    AS words
        FROM sensitive_trigger_log
        <where>
            <if test="userIds != null and userIds.size() > 0">
                user_id IN
                <foreach item="id" collection="userIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND trigger_time BETWEEN #{startDateTime} AND #{endDateTime}
        </where>
        GROUP BY words, words_id
        ORDER BY wordsNum DESC
        LIMIT 10
    </select>

    <select id="selectByUserAndDate" resultType="com.guosen.ewas.wechat.sensitive.domain.vo.SensitiveTriggerLogDate">
        SELECT count(*) AS                                triggerNum,
               DATE_FORMAT(trigger_time, '${dateFormat}') time
        FROM sensitive_trigger_log
        <where>
            <if test="userIds != null and userIds.size() > 0">
                user_id IN
                <foreach item="id" collection="userIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND trigger_time BETWEEN #{startDateTime} AND #{endDateTime}
        </where>
        GROUP BY time
    </select>

    <resultMap id="SenTriLogBOMap" type="com.guosen.ewas.wechat.sensitive.domain.bo.SenTriggerLogBO">
        <id column="id" property="id"/>
        <result column="rule_name" property="ruleName"/>
        <result column="words" property="words"/>
        <result column="msg_id" property="msgId"/>
        <result column="msg_type" property="msgType"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="trigger_id" property="triggerId"/>
        <result column="trigger_time" property="triggerTime"/>
        <result column="user_id" property="userId"/>
        <result column="group_id" property="groupId"/>
        <association property="comment" javaType="com.guosen.ewas.wechat.sensitive.domain.vo.SenCommentVO">
            <result column="com_id" property="id"/>
            <result column="com_sensitive_log_id" property="sensitiveLogId"/>
            <result column="com_comment" property="content"/>
            <result column="com_creator_name" property="creatorName"/>
            <result column="com_creator_no" property="creatorNo"/>
            <result column="com_submit_type" property="submitType"/>
            <result column="com_create_datetime" property="createDatetime"/>
            <collection property="commentFiles" ofType="com.guosen.ewas.wechat.sensitive.domain.vo.SenCommentFileVO">
                <result column="com_file_id" property="id"/>
                <result column="com_file_comment_id" property="commentId"/>
                <result column="com_file_name" property="fileName"/>
                <result column="com_file_path" property="filePath"/>
                <result column="com_file_md5sum" property="fileMd5sum"/>
                <result column="com_file_sync" property="fileSync"/>
            </collection>
        </association>
    </resultMap>

    <select id="selectByReqVO" resultMap="SenTriLogBOMap"
            parameterType="com.guosen.ewas.wechat.sensitive.domain.vo.SensitiveTriggerLogPageReqVO">
        SELECT sr.rule_name,
               stl.id,
               stl.words,
               stl.msg_id,
               stl.msg_type,
               stl.trigger_type,
               stl.trigger_id,
               stl.trigger_time,
               stl.user_id,
               stl.group_id,
               stc.id               as com_id,
               stc.sensitive_log_id as com_sensitive_log_id,
               stc.content          as com_comment,
               stc.creator_name     as com_creator_name,
               stc.creator_no       as com_creator_no,
               stc.submit_type      as com_submit_type,
               stc.create_datetime  as com_create_datetime,
               stcf.id              as com_file_id,
               stcf.comment_id      as com_file_comment_id,
               stcf.file_name       as com_file_name,
               stcf.file_path       as com_file_path,
               stcf.file_md5sum     as com_file_md5sum,
               stcf.file_sync       as com_file_sync
        FROM sensitive_trigger_log stl
                 LEFT JOIN sensitive_rule sr ON stl.sensitive_rule_id = sr.id
                 LEFT JOIN sensitive_trigger_comment stc on stl.id = stc.sensitive_log_id
                 LEFT JOIN sensitive_trigger_comment_file stcf on stc.id = stcf.comment_id
        <where>
            <if test="userIds != null and userIds.size() > 0">
                stl.user_id IN
                <foreach collection="userIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="words != null and words != ''">
                AND stl.words LIKE concat('%', #{words}, '%')
            </if>
            <if test="startDateTime != null">
                AND stl.trigger_time &gt;= #{startDateTime}
            </if>
            <if test="endDateTime != null">
                AND stl.trigger_time &lt;= #{endDateTime}
            </if>
            <if test="id != null">
                AND stl.id = #{id}
            </if>
            <if test="commentUserNo != null and commentUserNo != ''">
                AND stc.creator_no = #{commentUserNo}
            </if>
            <if test="commentUserIds != null and commentUserIds.size() != 0">
                AND stc.creator IN
                <foreach collection="commentUserIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>