<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.moment.dao.MomentTaskMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.moment.domain.entity.MomentTask">
        <!--@mbg.generated-->
        <!--@Table moment_task-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="moment_id" jdbcType="VARCHAR" property="momentId"/>
        <result column="userid" jdbcType="VARCHAR" property="userid"/>
        <result column="publish_status" jdbcType="TINYINT" property="publishStatus"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="modify_datetime" jdbcType="TIMESTAMP" property="modifyDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        moment_id,
        userid,
        publish_status,
        create_datetime,
        modify_datetime,
        remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from moment_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from moment_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.moment.domain.entity.MomentTask" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into moment_task (moment_id, userid, publish_status,
                                 create_datetime, modify_datetime, remark)
        values (#{momentId,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{publishStatus,jdbcType=TINYINT},
                #{createDatetime,jdbcType=TIMESTAMP}, #{modifyDatetime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.moment.domain.entity.MomentTask" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into moment_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="momentId != null">
                moment_id,
            </if>
            <if test="userid != null">
                userid,
            </if>
            <if test="publishStatus != null">
                publish_status,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="modifyDatetime != null">
                modify_datetime,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="momentId != null">
                #{momentId,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                #{userid,jdbcType=VARCHAR},
            </if>
            <if test="publishStatus != null">
                #{publishStatus,jdbcType=TINYINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyDatetime != null">
                #{modifyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.guosen.ewas.wechat.moment.domain.entity.MomentTask">
        <!--@mbg.generated-->
        update moment_task
        <set>
            <if test="momentId != null">
                moment_id = #{momentId,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                userid = #{userid,jdbcType=VARCHAR},
            </if>
            <if test="publishStatus != null">
                publish_status = #{publishStatus,jdbcType=TINYINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyDatetime != null">
                modify_datetime = #{modifyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.guosen.ewas.wechat.moment.domain.entity.MomentTask">
        <!--@mbg.generated-->
        update moment_task
        set moment_id       = #{momentId,jdbcType=VARCHAR},
            userid          = #{userid,jdbcType=VARCHAR},
            publish_status  = #{publishStatus,jdbcType=TINYINT},
            create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            modify_datetime = #{modifyDatetime,jdbcType=TIMESTAMP},
            remark          = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="insertBatch">
        insert into moment_task(moment_id, userid, publish_status, remark)
        values
        <foreach collection="momentTaskCollection" item="item" separator=",">
            (#{item.momentId,jdbcType=VARCHAR}, #{item.userid,jdbcType=VARCHAR},
             #{item.publishStatus,jdbcType=NUMERIC}, #{item.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <select id="selectOneByMomentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from moment_task
        where moment_id = #{momentId,jdbcType=VARCHAR}
        limit 1
    </select>
    <update id="updateStatusByMomentIdAndUserid">
        update moment_task
        set publish_status = #{publishStatus,jdbcType=TINYINT}
        where moment_id = #{momentId,jdbcType=VARCHAR}
          and userid = #{userid,jdbcType=VARCHAR}
    </update>

    <select id="groupByMomentIds" resultType="com.guosen.ewas.wechat.moment.domain.MomentCountBO">
        SELECT moment_id                         as momentId,
               SUM(IF(publish_status = 0, 1, 0)) AS unsentCount,
               SUM(IF(publish_status = 1, 1, 0)) AS sentCount
        FROM moment_task
        <where>
            <if test="momentIds != null and momentIds.size() &gt; 0">
                moment_id IN
                <foreach close=")" collection="momentIds" item="momentId" open="(" separator=",">
                    #{momentId}
                </foreach>
            </if>
        </where>
        GROUP BY moment_id
    </select>

    <select id="selectByMomentTaskReq" resultType="com.guosen.ewas.wechat.moment.domain.vo.MomentTaskVO">
        select u.name,
               mt.moment_id       as momentId,
               mt.userid,
               mt.publish_status  as publishStatus,
               mt.create_datetime as createDatetime
        from moment_task mt
                 left join user u on mt.userid = u.userid
        <where>
            <if test="momentId != null">
                and mt.moment_id = #{momentId}
            </if>
            <if test="publishStatus != null">
                and mt.publish_status = #{publishStatus}
            </if>
            <if test="name != null">
                and u.name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-06-07-->
    <select id="selectOneByMomentIdAndUserid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from moment_task
        where moment_id = #{momentId,jdbcType=VARCHAR}
          and userid = #{userid,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>