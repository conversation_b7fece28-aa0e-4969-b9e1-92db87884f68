<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.sensitive.dao.SensitiveTriggerCommentFileMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerCommentFile">
        <!--@mbg.generated-->
        <!--@Table sensitive_trigger_comment_file-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="comment_id" jdbcType="BIGINT" property="commentId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="file_md5sum" jdbcType="VARCHAR" property="fileMd5sum"/>
        <result column="file_sync" jdbcType="TINYINT" property="fileSync"/>
        <result column="is_delete" jdbcType="BOOLEAN" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        comment_id,
        file_name,
        file_path,
        file_md5sum,
        file_sync,
        is_delete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sensitive_trigger_comment_file
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from sensitive_trigger_comment_file
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerCommentFile"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sensitive_trigger_comment_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="commentId != null">
                comment_id,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
            <if test="filePath != null and filePath != ''">
                file_path,
            </if>
            <if test="fileMd5sum != null and fileMd5sum != ''">
                file_md5sum,
            </if>
            <if test="fileSync != null">
                file_sync,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="commentId != null">
                #{commentId,jdbcType=BIGINT},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null and filePath != ''">
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileMd5sum != null and fileMd5sum != ''">
                #{fileMd5sum,jdbcType=VARCHAR},
            </if>
            <if test="fileSync != null">
                #{fileSync,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.guosen.ewas.wechat.sensitive.domain.po.SensitiveTriggerCommentFile">
        <!--@mbg.generated-->
        update sensitive_trigger_comment_file
        <set>
            <if test="commentId != null">
                comment_id = #{commentId,jdbcType=BIGINT},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null and filePath != ''">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileMd5sum != null and fileMd5sum != ''">
                file_md5sum = #{fileMd5sum,jdbcType=VARCHAR},
            </if>
            <if test="fileSync != null">
                file_sync = #{fileSync,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BOOLEAN},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-08-->
    <update id="updateCommentIdByIds">
        update sensitive_trigger_comment_file
        set comment_id=#{updatedCommentId,jdbcType=BIGINT}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-08-->
    <update id="updateCommentIdByCommentId">
        update sensitive_trigger_comment_file
        set comment_id=#{updatedCommentId,jdbcType=BIGINT}
        where comment_id = #{commentId,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-08-->
    <select id="selectByCommentIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sensitive_trigger_comment_file
        where comment_id in
        <foreach item="item" index="index" collection="commentIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>