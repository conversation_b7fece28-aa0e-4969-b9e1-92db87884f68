<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guosen.ewas.wechat.msg.dao.MsgInfoMapper">
    <resultMap id="BaseResultMap" type="com.guosen.ewas.wechat.msg.domain.entity.MsgInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="msg_seq" jdbcType="BIGINT" property="msgSeq"/>
        <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="recall" jdbcType="BOOLEAN" property="recall"/>
        <result column="external" jdbcType="BOOLEAN" property="external"/>
        <result column="single" jdbcType="BOOLEAN" property="single"/>
        <result column="send_type" jdbcType="INTEGER" property="sendType"/>
        <result column="msg_type" jdbcType="INTEGER" property="msgType"/>
        <result column="from_id" jdbcType="BIGINT" property="fromId"/>
        <result column="receive_id" jdbcType="BIGINT" property="receiveId"/>
        <result column="msg_timestamp" jdbcType="BIGINT" property="msgTimestamp"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_id_max" jdbcType="BIGINT" property="userIdMax"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="relation" jdbcType="VARCHAR" property="relation"/>
        <result column="chat_data" jdbcType="VARCHAR" property="chatData"/>
        <result column="from" jdbcType="VARCHAR" property="from"/>
        <result column="receive" jdbcType="VARCHAR" property="receive"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        `msg_seq`,
        `msg_id`,
        `content`,
        `recall`,
        `external`,
        `single`,
        `send_type`,
        `msg_type`,
        `from_id`,
        `receive_id`,
        `msg_timestamp`,
        `from`,
        `receive`,
        `user_id`,
        `user_id_max`,
        `customer_id`,
        `group_id`,
        `relation`,
        `chat_data`,
        `create_datetime`
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${table}
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByMsgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${table}
        where msg_id = #{msgId,jdbcType=VARCHAR}
        limit 1
    </select>

    <insert id="insert" parameterType="com.guosen.ewas.wechat.msg.domain.entity.MsgInfo">
        INSERT INTO ${table} ( `msg_seq`, `msg_id`, `content`, `recall`
                             , `external`, `single`, `send_type`, `msg_type`
                             , `from_id`, `receive_id`, `msg_timestamp`, `relation`, `chat_data`
                             , `from`, `receive`, `user_id`, `user_id_max`, `customer_id`, `group_id`)
        VALUES ( #{record.msgSeq,jdbcType=BIGINT}, #{record.msgId,jdbcType=VARCHAR}, #{record.content,jdbcType=VARCHAR}
               , #{record.recall,jdbcType=TINYINT}, #{record.external,jdbcType=TINYINT}
               , #{record.single,jdbcType=TINYINT}
               , #{record.sendType,jdbcType=INTEGER}, #{record.msgType,jdbcType=INTEGER}
               , #{record.fromId,jdbcType=BIGINT}
               , #{record.receiveId,jdbcType=BIGINT}, #{record.msgTimestamp,jdbcType=BIGINT}
               , #{record.relation,jdbcType=VARCHAR}, #{record.chatData,jdbcType=VARCHAR}
               , #{record.from,jdbcType=VARCHAR}, #{record.receive,jdbcType=VARCHAR}
               , #{record.userId,jdbcType=BIGINT}, #{record.userIdMax,jdbcType=BIGINT}
               , #{record.customerId,jdbcType=BIGINT}, #{record.groupId,jdbcType=BIGINT})
    </insert>

    <insert id="insertBatch">
        INSERT INTO ${table}
        ( `msg_seq`, `msg_id`, `content`, `recall`
        , `external`, `single`, `send_type`, `msg_type`
        , `from_id`, `receive_id`, `msg_timestamp`, `relation`
        , `chat_data`
        , `from`, `receive`, `user_id`, `user_id_max`, `customer_id`, `group_id`)
        VALUES
        <foreach collection="records" item="item" index="index" separator=",">
            (#{item.msgSeq,jdbcType=BIGINT}, #{item.msgId,jdbcType=VARCHAR}, #{item.content,jdbcType=VARCHAR}
                , #{item.recall,jdbcType=TINYINT},#{item.external,jdbcType=TINYINT}, #{item.single,jdbcType=TINYINT}
                , #{item.sendType,jdbcType=INTEGER}, #{item.msgType,jdbcType=INTEGER},#{item.fromId,jdbcType=BIGINT}
                , #{item.receiveId,jdbcType=BIGINT}, #{item.msgTimestamp,jdbcType=BIGINT}, #{item.relation,jdbcType=VARCHAR}
                ,#{item.chatData,jdbcType=VARCHAR}
                , #{item.from,jdbcType=VARCHAR}, #{item.receive,jdbcType=VARCHAR}
                , #{item.userId,jdbcType=BIGINT}, #{item.userIdMax,jdbcType=BIGINT}
                , #{item.customerId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateByMsgId">
        update ${tableName}
        <set>
            <if test="msgInfo.msgSeq != null">
                msg_seq = #{msgInfo.msgSeq,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.content != null">
                content = #{msgInfo.content,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo.recall != null">
                recall = #{msgInfo.recall,jdbcType=TINYINT},
            </if>
            <if test="msgInfo.external != null">
                external = #{msgInfo.external,jdbcType=TINYINT},
            </if>
            <if test="msgInfo.single != null">
                single = #{msgInfo.single,jdbcType=TINYINT},
            </if>
            <if test="msgInfo.sendType != null">
                send_type = #{msgInfo.sendType,jdbcType=INTEGER},
            </if>
            <if test="msgInfo.msgType != null">
                msg_type = #{msgInfo.msgType,jdbcType=INTEGER},
            </if>
            <if test="msgInfo.fromId != null">
                from_id = #{msgInfo.fromId,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.receiveId != null">
                receive_id = #{msgInfo.receiveId,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.relation != null">
                relation = #{msgInfo.relation,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo.msgTimestamp != null">
                msg_timestamp = #{msgInfo.msgTimestamp,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.chatData != null">
                chat_data = #{msgInfo.chatData,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.userId != null">
                user_id = #{msgInfo.userId,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.userIdMax != null">
                user_id_max = #{msgInfo.userIdMax,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.customerId != null">
                customer_id = #{msgInfo.customerId,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.groupId != null">
                group_id = #{msgInfo.groupId,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.from != null">
                `from` = #{msgInfo.from,jdbcType=BIGINT},
            </if>
            <if test="msgInfo.receive != null">
                `receive` = #{msgInfo.receive,jdbcType=BIGINT},
            </if>
        </set>
        where msg_id = #{msgInfo.msgId,jdbcType=VARCHAR}
    </update>

    <select id="selectAllByTable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
    </select>

    <select id="getMinIdByTable" resultType="java.lang.Long">
        select IFNULL(min(id), 0) id
        from ${tableName}
    </select>

    <select id="getMaxIdByTable" resultType="java.lang.Long">
        select IFNULL(max(id), 0) id
        from ${tableName}
    </select>

    <select id="getMaxMsgSeqByTable" resultType="java.lang.Long">
        select IFNULL(max(msg_seq), 0) msg_seq
        from ${tableName}
    </select>

    <select id="getMinMsgSeqByTable" resultType="java.lang.Long">
        select IFNULL(min(msg_seq), 0) msg_seq
        from ${tableName}
    </select>

    <select id="selectByMsgIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${table}
        where msg_id in
        <foreach collection="msgIds" item="msgId" open="(" separator="," close=")">
            #{msgId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByTableAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where id &lt;= #{id}
        order by id desc
        limit #{num}
    </select>

    <select id="selectByTableAndNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${table}
        order by id
        limit #{num}, 1000
    </select>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS ${tableName}
        (
            `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
            `msg_seq`         bigint       DEFAULT NULL COMMENT '消息流水号',
            `msg_id`          varchar(255) DEFAULT NULL COMMENT '消息ID',
            `content`         longtext     DEFAULT NULL COMMENT '解析后的消息内容。',
            `recall`          tinyint(1)   DEFAULT 0 COMMENT '1-撤回消息，0-非撤回消息',
            `external`        tinyint(1)   DEFAULT NULL COMMENT '是否为外部聊天：1-外部聊天，0-内部聊天',
            `single`          tinyint(1)   DEFAULT NULL COMMENT '是否为单聊：1-单聊，0-群聊',
            `send_type`       int          DEFAULT NULL COMMENT '发送者类型：详情见SendType',
            `msg_type`        int          DEFAULT NULL COMMENT '消息类型，详情见MessageEnum',
            `from_id`         bigint       DEFAULT NULL COMMENT '发送人',
            `receive_id`      bigint       DEFAULT NULL COMMENT '接收人',
            `msg_timestamp`   bigint       DEFAULT NULL COMMENT '消息的时间戳',
            `from`            varchar(255) DEFAULT NULL COMMENT '微信发送人id',
            `receive`         varchar(255) DEFAULT NULL COMMENT '微信接收人id',
            `user_id`         bigint       DEFAULT NULL COMMENT '员工ID(员工2员工，小ID，默认)',
            `user_id_max`     bigint       DEFAULT NULL COMMENT '员工ID(员工2员工，大ID)',
            `customer_id`     bigint       DEFAULT NULL COMMENT '客户ID',
            `group_id`        bigint       DEFAULT NULL COMMENT '群ID',
            `relation`        varchar(100) DEFAULT NULL COMMENT '消息组合关系',
            `chat_data`       longtext COMMENT '完整的数据消息。未经过解析的。',
            `create_datetime` datetime(6)  DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据创建时间。',
            PRIMARY KEY (`id`),
            KEY `idx_msg_id` (`msg_id`),
            KEY `idx_user_id` (`user_id`, `user_id_max`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_group_id` (`group_id`),
            KEY `idx_relation` (`relation`),
            KEY `idx_from_id` (`from_id`),
            KEY `idx_msg_timestamp` (`msg_timestamp`),
            FULLTEXT KEY `idx_content_fulltext` (`content`) /*!50100 WITH PARSER `ngram` */
        ) ENGINE = InnoDB
          AUTO_INCREMENT = ${idStart}
          DEFAULT CHARSET = utf8mb4 COMMENT ='会话记录存储表';
    </update>

    <!--auto generated by MybatisCodeHelper on 2022-06-01-->
    <select id="selectByMsgInfoQueryBO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="msgSeq != null">
                and msg_seq=#{msgSeq,jdbcType=BIGINT}
            </if>
            <if test="msgId != null and msgId != ''">
                and msg_id=#{msgId,jdbcType=VARCHAR}
            </if>
            <choose>
                <when test="likeContent == true">
                    <if test="content != null and content != ''">
                        AND content LIKE CONCAT('%'
                                               , #{content,jdbcType=VARCHAR}
                                               , '%')
                    </if>
                    <if test="excludeContentList != null and excludeContentList.size() != 0">
                        <foreach collection="excludeContentList" item="item" open="AND "
                                 separator=" AND " close="">
                            content NOT LIKE CONCAT('%'
                                                   , #{item,jdbcType=VARCHAR}
                                                   , '%')
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    <!-- 使用双引号保护词组 或者 前缀符号+ 进行精确匹配 -->
                    <if test="content != null and content != ''">
                        AND MATCH (content) AGAINST (#{content,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                    <if test="excludeContent != null and excludeContent != ''">
                        AND NOT MATCH (content) AGAINST (#{excludeContent,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                </otherwise>
            </choose>
            <if test="recall != null">
                and recall=#{recall,jdbcType=TINYINT}
            </if>
            <if test="external != null">
                and `external`=#{external,jdbcType=TINYINT}
            </if>
            <if test="single != null">
                and single=#{single,jdbcType=TINYINT}
            </if>
            <if test="sendType != null">
                and send_type=#{sendType,jdbcType=INTEGER}
            </if>
            <if test="msgType != null">
                and msg_type=#{msgType,jdbcType=INTEGER}
            </if>
            <if test="fromId != null">
                and from_id =#{fromId,jdbcType=BIGINT}
            </if>
            <if test="receiveId != null">
                and receive_id=#{receiveId,jdbcType=BIGINT}
            </if>
            <if test="relation != null and relation != ''">
                and relation=#{relation,jdbcType=VARCHAR}
            </if>
            <if test="startTimestamp != null">
                and msg_timestamp &gt;= #{startTimestamp,jdbcType=BIGINT}
            </if>
            <if test="endTimestamp != null">
                and msg_timestamp &lt;= #{endTimestamp,jdbcType=BIGINT}
            </if>
            <if test="createDateTime != null">
                and create_datetime=#{createDateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        <choose>
            <when test="pageUp">
                ORDER BY msg_timestamp desc
            </when>
            <otherwise>
                ORDER BY msg_timestamp
            </otherwise>
        </choose>
        <if test="pageSize != null">
            limit #{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectGlobalByMsgInfoQueryBO" resultMap="BaseResultMap">
        SELECT ms.id,
        ms.`msg_seq`,
        ms.`msg_id`,
        ms.`content`,
        ms.`recall`,
        ms.`external`,
        ms.`single`,
        ms.`send_type`,
        ms.`msg_type`,
        ms.`from_id`,
        ms.`receive_id`,
        ms.`msg_timestamp`,
        ms.`relation`,
        ms.`chat_data`,
        ms.`create_datetime`,
        ms.`from`,
        ms.`receive`,
        ms.`user_id`,
        ms.`user_id_max`,
        ms.`customer_id`,
        ms.`group_id`
        FROM ${tableName} ms
        <where>
            <if test="external != null">
                AND ms.external = #{external,jdbcType=TINYINT}
            </if>
            <if test="startTimestamp != null">
                AND ms.msg_timestamp &gt;= #{startTimestamp,jdbcType=BIGINT}
            </if>
            <if test="endTimestamp != null">
                AND ms.msg_timestamp &lt;= #{endTimestamp,jdbcType=BIGINT}
            </if>
            <if test="single != null">
                AND ms.single = #{single,jdbcType=TINYINT}
            </if>
            <if test="!single and groupIds != null and groupIds.size() != 0">
                AND ms.group_id in
                <foreach collection="groupIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="customerIds != null and customerIds.size() != 0">
                AND ms.customer_id in
                <foreach collection="customerIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() != 0">
                <choose>
                    <when test="external">
                        <!-- 外部聊天，只需要查 user_id 即可. -->
                        AND ms.user_id in
                        <foreach collection="userIds" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </when>
                    <otherwise>
                        <!-- 内部聊天，只需要查 发送员工 即可. -->
                        AND ms.from_id in
                        <foreach collection="userIds" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="groupMsgFlag != null and groupMsgFlag != ''">
                AND ms.content not like concat(#{groupMsgFlag,jdbcType=VARCHAR}, '%')
            </if>
            <choose>
                <when test="likeContent == true">
                    <if test="content != null and content != ''">
                        AND ms.content LIKE CONCAT('%', #{content,jdbcType=VARCHAR}, '%')
                    </if>
                    <if test="excludeContentList != null and excludeContentList.size() != 0">
                        <foreach collection="excludeContentList" item="item" open="AND "
                                 separator=" AND " close="">
                            ms.content NOT LIKE CONCAT('%', #{item,jdbcType=VARCHAR}, '%')
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    <if test="content != null and content != ''">
                        AND MATCH(ms.content) AGAINST(#{content,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                    <if test="excludeContent != null and excludeContent != ''">
                        AND NOT MATCH(ms.content) AGAINST(#{excludeContent,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                </otherwise>
            </choose>
        </where>
        <choose>
            <when test="pageUp">
                ORDER BY msg_timestamp desc
            </when>
            <otherwise>
                ORDER BY msg_timestamp
            </otherwise>
        </choose>
        <choose>
            <when test="limitStart != null and limitEnd != null">
                limit #{limitStart,jdbcType=INTEGER}, #{limitEnd,jdbcType=INTEGER}
            </when>
            <otherwise>
                limit #{pageSize,jdbcType=INTEGER}
            </otherwise>
        </choose>
    </select>

    <select id="existsByMsgIds" resultType="java.lang.Long">
        select id
        from ${table}
        where msg_id in
        <foreach collection="msgIds" item="msgId" open="(" separator="," close=")">
            #{msgId,jdbcType=VARCHAR}
        </foreach>
        limit 1;
    </select>
    <select id="countByMsgInfoQueryBO" resultType="java.lang.Long"
            parameterType="com.guosen.ewas.wechat.msg.domain.bo.MsgInfoQueryBO">
        SELECT count(ms.id)
        FROM ${tableName} ms
        <where>
            <if test="external != null">
                AND ms.external = #{external,jdbcType=TINYINT}
            </if>
            <if test="startTimestamp != null">
                AND ms.msg_timestamp &gt;= #{startTimestamp,jdbcType=BIGINT}
            </if>
            <if test="endTimestamp != null">
                AND ms.msg_timestamp &lt;= #{endTimestamp,jdbcType=BIGINT}
            </if>
            <if test="single != null">
                AND ms.single = #{single,jdbcType=TINYINT}
            </if>
            <if test="(null != single and !single) and groupIds != null and groupIds.size() != 0">
                AND ms.group_id in
                <foreach collection="groupIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="customerIds != null and customerIds.size() != 0">
                AND ms.customer_id in
                <foreach collection="customerIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() != 0">
                <choose>
                    <when test="external">
                        <!-- 外部聊天，只需要查 user_id 即可. -->
                        AND ms.user_id in
                        <foreach collection="userIds" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </when>
                    <otherwise>
                        <!-- 内部聊天，只需要查 发送员工 即可. -->
                        AND ms.from_id in
                        <foreach collection="userIds" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="groupMsgFlag != null and groupMsgFlag != ''">
                AND ms.content not like concat(#{groupMsgFlag,jdbcType=VARCHAR}, '%')
            </if>
            <choose>
                <when test="likeContent == true">
                    <if test="content != null and content != ''">
                        AND ms.content LIKE CONCAT('%', #{content,jdbcType=VARCHAR}, '%')
                    </if>
                    <if test="excludeContentList != null and excludeContentList.size() != 0">
                        <foreach collection="excludeContentList" item="item" open="AND "
                                 separator=" AND " close="">
                            ms.content NOT LIKE CONCAT('%', #{item,jdbcType=VARCHAR}, '%')
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    <if test="content != null and content != ''">
                        AND MATCH(ms.content) AGAINST(#{content,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                    <if test="excludeContent != null and excludeContent != ''">
                        AND NOT MATCH(ms.content) AGAINST(#{excludeContent,jdbcType=VARCHAR} IN BOOLEAN MODE)
                    </if>
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>