package com.guosen.ewas.wechat.msg.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.guosen.commonsrv.zebra.anotation.ZebraController;
import com.guosen.commonsrv.zebra.anotation.ZebraMapping;
import com.guosen.commonsrv.zebra.anotation.ZebraParam;
import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.common.exception.BusinessException;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.common.util.FastJsonUtils;
import com.guosen.ewas.wechat.jxh.service.JxhService;
import com.guosen.ewas.wechat.msg.domain.vo.GlobalMsgVO;
import com.guosen.ewas.wechat.msg.domain.vo.InvestmentAdviceVO;
import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgPositionVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgGlobalReqVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgSingleReqVO;
import com.guosen.ewas.wechat.msg.enums.MessageEnum;
import com.guosen.ewas.wechat.msg.service.IMsgSearchService;
import com.guosen.ewas.wechat.msg.service.MsgToolsService;
import com.guosen.ewas.wechat.score.domain.vo.MsgFlagDetailListVO;
import com.guosen.ewas.wechat.score.service.MsgFlagService;
import com.guosen.ewas.wechat.sensitive.domain.vo.SenWordVO;
import com.guosen.ewas.wechat.sensitive.service.SenThesaurusService;
import com.guosen.jxhcommon.contracts.ResponseContract;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话存档查询控制器
 *
 * <AUTHOR>
 */
@Slf4j
@ZebraController
@RequiredArgsConstructor
public class MsgController {
    private final IMsgSearchService msgSearchService;
    private final MsgFlagService msgFlagService;
    private final MsgToolsService msgToolsService;
    private final SenThesaurusService senThesaurusService;
    private final JxhService jxhService;
    private final ConfigKvService configKvService;


    /**
     * 全局检索
     *
     * @apiNote 不确定对话类型查询，全局聊天信息查询
     */
    @ZebraMapping("msg/info")
    public ResponseContract<PageWrapVO<GlobalMsgVO>> messageInfo(@ZebraParam("body") String body) {
        return ResponseContract.success(searchGlobal(body));
    }

    /**
     * 全局检索统计,异步执行
     *
     * @apiNote 全局检索统计,异步执行
     */
    @ZebraMapping("msg/info/count")
    public ResponseContract<Integer> messageCount(@ZebraParam("body") String body) {
        MsgGlobalReqVO search = FastJsonUtils.json2Obj(body, MsgGlobalReqVO.class);
        return ResponseContract.success(0);
    }

    /**
     * 会话检索-单聊/群聊
     *
     * @apiNote 确定对话类型查询，可查询单聊/群聊。比如: 员工会话查询、客户会话查询
     */
    @ZebraMapping("msg/single/page")
    public ResponseContract<PageWrapVO<MessageVO>> selectMessageSinglePage(@ZebraParam("body") String body) {
        PageWrapVO<MsgSingleReqVO> page = FastJsonUtils.json2Obj(body, new TypeReference<PageWrapVO<MsgSingleReqVO>>() {
        });
        PageWrapVO<MessageVO> result = new PageWrapVO<>();
        MsgSingleReqVO search = page.getSearch();
        search.setPageSize(search.getPageSize() == null ? page.getPageSize() : search.getPageSize());
        result.setList(msgSearchService.selectSingle(search));
        addExtra(result.getList());
        return ResponseContract.success(result);
    }

    /**
     * 消息定位检索
     */
    @ZebraMapping("msg/position")
    public ResponseContract<PageWrapVO<MessageVO>> messagePosition(@ZebraParam("body") String body) {
        PageWrapVO<MsgPositionVO> page = FastJsonUtils.json2Obj(body, new TypeReference<PageWrapVO<MsgPositionVO>>() {
        });
        PageWrapVO<MessageVO> result = new PageWrapVO<>();
        MsgPositionVO search = page.getSearch();
        search.setPageSize(search.getPageSize() == null ? page.getPageSize() : search.getPageSize());
        result.setList(msgSearchService.selectPosition(search));
        addExtra(result.getList());
        return ResponseContract.success(result);
    }

    /**
     * 获取智能提示词-敏感词匹配
     */
    @ZebraMapping("msg/intelligent/search")
    public ResponseContract<List<SenWordVO>> intelligentSearch(@ZebraParam("body") String body) {
        SenWordVO vo = FastJsonUtils.json2Obj(body, SenWordVO.class);
        if (vo == null) {
            return ResponseContract.success(Collections.emptyList());
        }
        List<SenWordVO> res = senThesaurusService.intelligentSearch(vo.getWord());
        return ResponseContract.success(res);
    }

    /**
     * 全量敏感词
     */
    @ZebraMapping("msg/sensitive/all")
    public ResponseContract<List<SenWordVO>> sensitiveAll() {
        return ResponseContract.success(senThesaurusService.selectEnableWords());
    }

    /**
     * 消息发送
     */
    @ZebraMapping("msg/send")
    public ResponseContract<String> sendMsg(@ZebraParam("msgId") String msgId) {
        GlobalMsgVO vo = msgSearchService.getByMsgId(msgId);
        if (null == vo) {
            return ResponseContract.success("消息不存在。");
        } else {
            msgToolsService.sendMsgToQyWx(vo);
            return ResponseContract.success("消息发送成功。");
        }
    }

    /**
     * 添加补充信息
     */
    public void addExtra(List<? extends MessageVO> msgList) {
        if (CollUtil.isEmpty(msgList)) {
            return;
        }
        Set<String> msgIds = msgList.stream().filter(i -> MessageEnum.TEXT.equals(i.getMsgTypeEnum()))
                .map(MessageVO::getMsgId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(msgIds)) {
            Map<String, InvestmentAdviceVO> tzjyMap = BeanUtil.copyToList(jxhService.getByMsgIds(msgIds), InvestmentAdviceVO.class).stream()
                    .collect(Collectors.toMap(InvestmentAdviceVO::getMsgid, i -> i, (i, i2) -> i));
            // 设置标记
            Map<String, MsgFlagDetailListVO> flagMap = msgFlagService.selectAllByMsgIds(msgIds).stream()
                    .collect(Collectors.toMap(MsgFlagDetailListVO::getMsgId, e -> e));
            msgList.forEach(i -> {
                i.setInvestmentAdvice(tzjyMap.get(i.getMsgId()));
                Optional.ofNullable(flagMap.get(i.getMsgId())).ifPresent(f -> i.setFlagTypes(f.getFlagTypes()));
            });
        }
    }

    private PageWrapVO<GlobalMsgVO> searchGlobal(String body) {
        PageWrapVO<MsgGlobalReqVO> page = FastJsonUtils.json2Obj(body, new TypeReference<PageWrapVO<MsgGlobalReqVO>>() {
        });
        MsgGlobalReqVO search = page.getSearch();
        if (search.getScope() == null) {
            throw new BusinessException("请选择员工 / 部门");
        }
        if (search.getStartTime() == null || search.getEndTime() == null) {
            throw new BusinessException("请选择查询时间");
        }
        String month = configKvService.getCacheValueByTypeAndKey(ConfigConsts.Type.SETTING, ConfigConsts.Key.MSG_QUERY_MONTH, "4");
        //　时间间隔不能超过 60 天
        if (search.getEndTime() - search.getStartTime() > 31 * Long.parseLong(month) * 24 * 60 * 60 * 1000) {
            throw new BusinessException("查询时间间隔不可超过 " + 31 * Long.parseLong(month) + " 天");
        }
        String type = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SYSTEM, "msg_search_global_type");
        if ("old".equals(type)) {
            PageWrapVO<GlobalMsgVO> result = new PageWrapVO<>();
            search.setPageSize(search.getPageSize() == null ? page.getPageSize() : search.getPageSize());
            result.setList(msgToolsService.handleCustomerProperty(msgSearchService.selectGlobal(search)));
            addExtra(result.getList());
            return result;
        } else {
            PageWrapVO<GlobalMsgVO> pageWrap = msgSearchService.selectGlobalPage(page);
            pageWrap.setList(msgToolsService.handleCustomerProperty(pageWrap.getList()));
            addExtra(pageWrap.getList());
            return pageWrap;
        }
    }

}