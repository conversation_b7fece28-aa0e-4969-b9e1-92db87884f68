package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.MeetingNotificationWrap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议控制消息
 * meeting_notification。String类型
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingNotificationDTO extends DataBaseDTO {

    /**
     * meeting
     */
    @JsonProperty("info")
    private MeetingNotificationWrap info;

}
