package com.guosen.ewas.wechat.reply.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.reply.dao.ReplyTimeoutRecordMapper;
import com.guosen.ewas.wechat.reply.domain.bo.ReplyTimeoutRecordBO;
import com.guosen.ewas.wechat.reply.domain.po.ReplyTimeoutRecord;
import com.guosen.ewas.wechat.reply.service.ReplyTimeoutRecordService;
import com.guosen.ewas.wechat.uc.domain.bo.ScopeBO;
import com.guosen.ewas.wechat.uc.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * the service implement of ReplyTimeoutRecord
 *
 * <AUTHOR>
 * @date 2021-09-03 18:07:57.0816
 */
@Service("replyTimeoutRecordService")
public class ReplyTimeoutRecordServiceImpl implements ReplyTimeoutRecordService {

    private final static Logger log = LoggerFactory.getLogger(ReplyTimeoutRecordServiceImpl.class);

    @Resource
    ReplyTimeoutRecordMapper replyTimeoutRecordMapper;

    @Resource
    UserService userService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        log.debug("Delete by id = {}", id);
        ReplyTimeoutRecord record = new ReplyTimeoutRecord();
        record.setId(id);
        record.setIsDelete(BusinessConsts.IS_DELETE);
        return updateByPrimaryKeySelective(record);
    }

    @Override
    public int insert(ReplyTimeoutRecord record) {
        return null == record ? 0 : replyTimeoutRecordMapper.insert(record);
    }

    @Override
    public int insertSelective(ReplyTimeoutRecord record) {
        return null == record ? 0 : replyTimeoutRecordMapper.insertSelective(record);
    }

    @Override
    public ReplyTimeoutRecord selectByPrimaryKey(Long id) {
        log.info("Get by id = {}", id);
        return null == id ? null : replyTimeoutRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ReplyTimeoutRecord record) {
        log.info("Update selective by id. {}", record);
        return null == record || null == record.getId() ? 0
                : replyTimeoutRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ReplyTimeoutRecord record) {
        log.info("Update by id. {}", record);
        return null == record || null == record.getId() ? 0 : replyTimeoutRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<ReplyTimeoutRecord> selectSelective(ReplyTimeoutRecord record) {
        log.info("select by {}", record);
        record = null == record ? new ReplyTimeoutRecord() : record;
        return replyTimeoutRecordMapper.selectSelective(record);
    }

    @Override
    public List<ReplyTimeoutRecord> selectByPrimarys(List<Long> ids) {
        return null == ids ? null : replyTimeoutRecordMapper.selectByPrimarys(ids);
    }

    @Override
    public PageInfo<ReplyTimeoutRecordBO> page(PageWrapVO<ReplyTimeoutRecordBO> record) {
        ReplyTimeoutRecordBO search = record.getSearch();
        ScopeBO scope = new ScopeBO();
        scope.setUserIds(userService.getAndCheckUserIds(search.getScope()));
        search.setScope(scope);
        return PageHelper.startPage(record.getPageNum(), record.getPageSize()).setOrderBy("r.trigger_datetime desc")
                .doSelectPageInfo(() -> selectByBo(record.getSearch()));
    }

    @Override
    public List<ReplyTimeoutRecordBO> selectByBo(ReplyTimeoutRecordBO record) {
        if (null == record) {
            return Collections.emptyList();
        }
        return replyTimeoutRecordMapper.selectByBo(record);
    }

}