package com.guosen.ewas.wechat.uc.domain.po;

import java.io.Serializable;

/**
 * 使用节点的部门和员工
 * <AUTHOR>
 */
public class NodeRelUserDepartment implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * node.id
     */
    private Long nodeId;

    /**
     * 关联的 id，user.id 或 department.id
     */
    private Long relId;

    /**
     * 关联的类型，1 = user或2 = department
     */
    private Integer type;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Long getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * node.id
     */
    public Long getNodeId() {
        return nodeId;
    }

    /**
     * node.id
     */
    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 关联的 id，user.id 或 department.id
     */
    public Long getRelId() {
        return relId;
    }

    /**
     * 关联的 id，user.id 或 department.id
     */
    public void setRelId(Long relId) {
        this.relId = relId;
    }

    /**
     * 关联的类型，1 = user或2 = department
     */
    public Integer getType() {
        return type;
    }

    /**
     * 关联的类型，1 = user或2 = department
     */
    public void setType(Integer type) {
        this.type = type;
    }
}