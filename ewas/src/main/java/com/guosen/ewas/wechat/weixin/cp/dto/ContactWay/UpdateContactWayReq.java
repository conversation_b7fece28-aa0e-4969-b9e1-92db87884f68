package com.guosen.ewas.wechat.weixin.cp.dto.ContactWay;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 注意：已失效的临时会话联系方式无法进行编辑
 *
 * <AUTHOR>
 * @date 2021/1/13 11:40
 */

public class UpdateContactWayReq extends ContactWayReq {

    /**
     * 企业联系方式的配置id
     */
    @JsonProperty("config_id")
    private String configId;

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    @Override
    public String toString() {
        return "UpdateContactWayReq{" +
                "configId='" + configId + '\'' +
                '}';
    }
}
