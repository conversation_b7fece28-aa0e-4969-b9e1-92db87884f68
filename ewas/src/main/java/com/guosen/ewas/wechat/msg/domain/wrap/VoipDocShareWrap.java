package com.guosen.ewas.wechat.msg.domain.wrap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VoipDocShareWrap {
    /**
     * 文档共享文件名称。String类型
     */
    @JsonProperty("filename")
    private String fileName;
    /**
     * 共享文件的md5值。String类型
     */
    @JsonProperty("md5sum")
    private String md5Sum;
    /**
     * 共享文件的大小。Uint64类型
     */
    @JsonProperty("filesize")
    private Long fileSize;
    /**
     * 共享文件的sdkfile，通过此字段进行媒体数据下载。String类型
     */
    @JsonProperty("sdkfileid")
    private String sdkFileId;

}
