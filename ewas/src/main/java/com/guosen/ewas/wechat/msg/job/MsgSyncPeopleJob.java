package com.guosen.ewas.wechat.msg.job;

import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.constant.RedisKeyConsts;
import com.guosen.ewas.wechat.common.redis.RedisDistributedLockHelper;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.msg.service.MsgSyncPeopleService;
import com.guosen.zebra.distributed.lock.DistributedLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 消息涉及人员同步处理定时器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MsgSyncPeopleJob {
    private final MsgSyncPeopleService msgSyncPeopleService;
    private final ConfigKvService configKvService;

    /**
     * 每 10分钟执行一次
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void msgSyncPeopleJob() {
        log.debug("Message sync people start.");
        try {
            msgSyncPeopleService.handleQueue();
        } catch (Exception e) {
            log.error("Message sync people error.", e);
        }
        log.debug("Message sync people end.");
    }

    /**
     * 同步虚拟人员／群
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void syncVirtualPeopleJob() {
        log.debug("Sync virtual people start");
        DistributedLock lock = RedisDistributedLockHelper.getLock(RedisKeyConsts.JobLock.SYNC_VIRTUAL_PEOPLE, 60L);
        if (lock == null || !lock.tryLock()) {
            log.debug("Sync virtual people lock(key = {}) fail. skip job. {}", RedisKeyConsts.JobLock.SYNC_VIRTUAL_PEOPLE, lock);
            return;
        }
        try {
            if ("true".equalsIgnoreCase(configKvService.getValueByTypeAndKey(ConfigConsts.Type.TEMP, "not_sync_virtual_people"))) {
                return;
            }
            msgSyncPeopleService.syncVirtualPeople();
        } catch (Exception e) {
            log.error("Sync virtual people lock error", e);
        } finally {
            lock.unlock();
        }
        log.debug("Sync virtual people end");
    }

}
