package com.guosen.ewas.wechat.customer.enums;

/**
 * 最近时间
 *
 * <AUTHOR>
 * @date 2021/9/9 9:44
 */
public enum RecentTimeType {

    //昨天
    YESTERDAY(1),
    //七日
    SEVEN(2),
    //三十天
    THIRTY(3),
    //自定义
    CUSTOM(4);

    private final Integer code;

    RecentTimeType(Integer code) {
        this.code = code;
    }

    public static RecentTimeType valueOf(Integer code) {
        RecentTimeType[] recentTimeTypes = values();
        for (RecentTimeType recentTimeType : recentTimeTypes) {
            if (recentTimeType.getCode().equals(code)) {
                return recentTimeType;
            }
        }
        return CUSTOM;
    }

    public Integer getCode() {
        return code;
    }
}
