package com.guosen.ewas.wechat.msg.domain.vo.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 语音转写对象 aliyun_nls
 *
 */
@Data
public class MsgVoiceTransVO {

    private Long id;

    private String msgid;

    private String fileLink;

    private String taskId;

    private Long status;

    private String statusMessage;

    private Integer type;

    private Long pullCount;

    private LocalDateTime pullDatetime;

    private LocalDateTime createDatetime;

    private LocalDateTime modifyDatetime;


}
