package com.guosen.ewas.wechat.common.constant;

/**
 * 配置使用常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-09-12 10:51
 */
public interface ConfigConsts {

    /**
     * 类型
     */
    interface Type {
        /**
         * 系统分类
         */
        String SYSTEM = "SYSTEM";

        /**
         * 转写分类
         */
        String TRANS = "TRANS";

        /**
         * ES 数据分类
         */
        String ES_DATA = "ES_DATA";

        /**
         * 临时数据分类
         */
        String TEMP = "TEMP";

        String TEMP_MIGRATE = "TEMP_MIGRATE";

        /**
         * 消息流水号分片获取操作分类
         */
        String MSG_SEQ = "MSG_SEQ";
        /**
         * 企业微信相关
         */
        String WORK_WECHAT = "WORK_WECHAT";
        /**
         * 自定义配置
         */
        String SETTING = "SETTING";
        /**
         * 国信精细化平台配置
         */
        String GUOSEN_JXH = "GUOSEN-JXH";
    }

    /**
     * key值
     */
    interface Key {
        /**
         * 消息流水号
         */
        String MSG_SEQ = "msgSeq";
        /**
         * 客服 SECRET
         */
        String KF_SECRET = "KF_SECRET";
        /**
         * 超时回复设置
         */
        String REPLY_TIMEOUT_SET = "REPLY_TIMEOUT_SET";

        /**
         * 数据范围是否开启
         */
        String DATA_SCOPE_ENABLED = "DATA_SCOPE_ENABLED";

        /**
         * 系统初始化用户同步
         */
        String INIT_USER_SYNC = "INIT_USER_SYNC";

        /**
         * 微信接口，每分钟调用限制次数
         */
        String API_LIMIT_MINUTE = "API_LIMIT_MINUTE";

        /**
         * 拉取消息时，员工或客户同步间隔时间（单位：小时）；默认4小时
         */
        String SYNC_INTERVAL = "SYNC_INTERVAL";

        /**
         * 文件队列大小，小文件队列中小于此值，则协助拉取大文件
         */
        String FILE_QUEUE_SIZE = "FILE_QUEUE_SIZE";

        /**
         * 语音翻译选择
         */
        String TRANS_SWITCH = "switch";

        /**
         * 是否存在迁移
         */
        String EXIST_MIGRATION = "EXIST_MIGRATION";

        /**
         * 目前迁移的表
         */
        String TABLE = "table";

        /**
         * 迁移的表的id
         */
        String ID = "id";

        /**
         * message_info_ 消息临时修复
         */
        String MSG_REPAIR = "message_info_repair";

        /**
         * 索引模板mapping
         */
        String TEMPLATE_MAPPING = "mapping";

        /**
         * 索引模板配置
         */
        String TEMPLATE_SETTING = "setting";

        /**
         * es中可查询的第一个索引的日期
         */
        String FIRST_INDEX_DATE = "first_index_date";

        /**
         * 临时兼容设计：
         * 客户名称是否MySQL全文搜索。
         * 默认全文搜索开启
         * 配置true，改为like搜索
         */
        String CUSTOMER_NAME_LIKE = "customer_name_like";

        /**
         * 内部文件是否需要下载(默认是true，需要下载)
         */
        String INTERNAL_FILE_DOWNLOAD = "internal_file_download";

        /**
         * 内部文件超过这个值就不下载；单位：MB
         */
        String INTERNAL_FILE_SIZE = "internal_file_size";

        /**
         * 微信消息通知人
         */
        String MSG_NOTIFIER = "msg_notifier";

        /**
         * 微信消息通知人
         */
        String MSG_NOTIFICATION_TIME = "msg_notification_time";

        /**
         * 需要修改后缀的后缀列表
         */
        String FILE_CHANGE_SUFFIX = "file_change_suffix";

        /**
         * 消息步长：默认10000
         */
        String MSG_SEQ_STEP = "msg_seq_step";

        /**
         * 消息步长分片长度：默认200
         */
        String MSG_SEQ_STEP_SEG = "msg_seq_step_seg";

        String OFFICE_PORTS = "office_ports";
        String OFFICE_TIMEOUT = "office_timeout";
        String OFFICE_HOME = "office_home";

        /**
         * 文件 转为 pdf 支持类型
         */
        String TO_PDF_TYPES = "TO_PDF_TYPES";

        /**
         * 消息是否加入备份表（message_info_*）
         */
        String ADD_MESSAGE_BACKUP = "ADD_MESSAGE_BACKUP";
        String HOST_FILE = "host_file";
        String HOST_VOICE = "host_voice";
        String OSS_URL_CHANGE = "oss_url_change";
        String OSS_URL_CHANGE_VOICE = "oss_url_change_voice";

        /**
         * 优秀话术 ID
         */
        String MSG_FLAG_GOOD_ID = "MSG_FLAG_GOOD_ID";
        /**
         * 问题话术 ID
         */
        String MSG_FLAG_QUESTIONS_ID = "MSG_FLAG_QUESTIONS_ID";

        /**
         * 国信总部 ID 集合
         */
        String GUOSEN_HEADQUARTERS_DEPARTMENT_IDS = "HQ_DEPT_IDS";

        /**
         * 健康监测：文件下载检测
         */
        String HEALTH_FILE_DOWNLOAD_FAIL = "health_file_download_fail";
        /**
         * 健康监测：文件下载等待数阈值
         */
        String HEALTH_FILE_DOWNLOAD_WAIT = "health_file_download_wait_threshold";
        /**
         * 统计数据：获取联系客户统计数据同步天数
         */
        String STATS_SYNC_USER_BEHAVIOR_DAYS = "stats_sync_user_behavior_days";

        /**
         * 头像是否下载到本地开关
         */
        String AVATAR_DOWNLOAD_LOCAL = "avatar_download_local";

        /**
         * 展示删除的客户开关
         */
        String SHOW_DEL_CUSTOMER = "show_del_customer";

        String FILE_PATH = "file_path";

        /**
         * 资源文件的目录
         */
        String RESOURCES_PATH = "resources_path";

        String MSG_QUERY_MONTH = "queryMonth";
    }


}
