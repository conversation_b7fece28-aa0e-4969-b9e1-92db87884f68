package com.guosen.ewas.wechat.customer.domain.entity;

import java.time.LocalDate;

/**
 * 群数据统计 group_chat_log
 *
 * <AUTHOR>
 * @date 2021-09-09 14:07:06
 */
public class GroupChatLog {

    /**
     * id
     */
    private Long id;

    /**
     * 客户群id
     */
    private Long groupId;

    /**
     * 客户人数
     */
    private Integer customerCount;

    /**
     * 员工人数
     */
    private Integer userCount;

    /**
     * 时间
     */
    private LocalDate statisticsDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getCustomerCount() {
        return customerCount;
    }

    public void setCustomerCount(Integer customerCount) {
        this.customerCount = customerCount;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public LocalDate getStatisticsDate() {
        return statisticsDate;
    }

    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }

    @Override
    public String toString() {
        return "GroupChatLog{" +
                "id=" + getId() +
                ", groupId=" + getGroupId() +
                ", cutomerCount=" + getCustomerCount() +
                ", userCount=" + getUserCount() +
                ", statisticsDate=" + getStatisticsDate() +
                '}';
    }
}
