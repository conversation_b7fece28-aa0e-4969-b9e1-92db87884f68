package com.guosen.ewas.wechat.customer.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.guosen.ewas.wechat.common.constant.DBConsts;
import com.guosen.ewas.wechat.common.constant.GroupConsts;
import com.guosen.ewas.wechat.common.util.DateUtils;
import com.guosen.ewas.wechat.customer.dao.GroupChatMemberMapper;
import com.guosen.ewas.wechat.customer.domain.bo.GroupMemberCountBO;
import com.guosen.ewas.wechat.customer.domain.bo.GroupStatisticsBO;
import com.guosen.ewas.wechat.customer.domain.entity.Customer;
import com.guosen.ewas.wechat.customer.domain.entity.GroupChat;
import com.guosen.ewas.wechat.customer.domain.entity.GroupChatMember;
import com.guosen.ewas.wechat.uc.domain.po.User;
import com.guosen.ewas.wechat.uc.service.UserService;
import com.guosen.ewas.wechat.weixin.cp.dto.customer.GroupChatDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 群成员Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-06 14:07:06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupChatMemberService {
    private final GroupChatMemberMapper groupChatMemberMapper;
    private final UserService userService;
    private final CustomerService customerService;
    private final GroupChatService groupChatService;

    public int insertBatch(List<GroupChatMember> members) {
        return CollUtil.isEmpty(members) ? DBConsts.FAIL : groupChatMemberMapper.insertBatch(members);
    }

    public int syncGroupMember(GroupChatDetailDTO groupChatDetailDTO, GroupChat groupChat) {
        List<GroupChatMember> groupChatMemberList = new ArrayList<>();
        for (GroupChatDetailDTO.GroupChat.MemberList member : groupChatDetailDTO.getGroupChat().getMemberList()) {
            GroupChatMember groupChatMember = Convert.convert(GroupChatMember.class, member);
            groupChatMember.setGroupId(groupChat.getId());
            if (member.getInvitor() != null && StrUtil.isNotEmpty(member.getInvitor().getUserid())) {
                groupChatMember.setInvitorUserid(member.getInvitor().getUserid());
            }
            LocalDateTime joinTime = DateUtils.s2Time(Long.valueOf(member.getJoinTime()));
            groupChatMember.setJoinTime(joinTime);
            groupChatMember.setMemberUserid(member.getUserid());
            if (null != groupChatMember.getType() && GroupConsts.MEMBER_TYPE_USER == groupChatMember.getType()) {
                Optional.ofNullable(userService.selectByUserid(groupChatMember.getMemberUserid()))
                        .ifPresent(i -> groupChatMember.setMemberId(i.getId()));
            } else if (null != groupChatMember.getType() && GroupConsts.MEMBER_TYPE_CUSTOMER == groupChatMember.getType()) {
                Optional.ofNullable(customerService.getByExternalUserid(groupChatMember.getMemberUserid()))
                        .ifPresent(i -> groupChatMember.setMemberId(i.getId()));
            }
            groupChatMemberList.add(groupChatMember);
        }
        //客户群成员为空
        log.info("group chat member size: {}", groupChatMemberList.size());
        //批量新增客户群成员
        if (CollUtil.isNotEmpty(groupChatMemberList)) {
            int insertBatch = groupChatMemberMapper.insertBatch(groupChatMemberList);
            handleGroupName(groupChatMemberList, groupChat);
            return insertBatch;
        } else {
            return DBConsts.FAIL;
        }
    }

    /**
     * 处理群名
     *
     * @param groupChatMemberList 群成员
     * @param groupChat           客户群
     */
    private void handleGroupName(List<GroupChatMember> groupChatMemberList, GroupChat groupChat) {
        //存在群名
        if (StrUtil.isNotEmpty(groupChat.getName())) {
            return;
        }
        List<String> memberNames = new ArrayList<>();
        //标识
        int index = 0;
        //遍历群成员
        for (GroupChatMember groupChatMember : groupChatMemberList) {
            //只取三个成员
            if (index == 3) {
                break;
            }
            //不存在成员类型或userid
            if (groupChatMember.getType() == null || groupChatMember.getMemberUserid() == null) {
                log.info("chat member type or member userid is null");
                continue;
            }
            //企业成员
            if (Integer.valueOf(1).equals(groupChatMember.getType())) {
                User user = userService.selectByUserid(groupChatMember.getMemberUserid());
                if (user != null && StrUtil.isNotEmpty(user.getName())) {
                    memberNames.add(user.getName());
                }
            }
            //客户
            if (Integer.valueOf(2).equals(groupChatMember.getType())) {
                Customer customer = customerService.getByExternalUserid(groupChatMember.getMemberUserid());
                if (customer != null && StrUtil.isNotEmpty(customer.getName())) {
                    memberNames.add(customer.getName());
                }
            }
            index += 1;
        }
        if (CollUtil.isEmpty(memberNames)) {
            log.error("member names is empty");
            return;
        }
        //设置群名
        groupChat.setName(String.join(",", memberNames) + "的客户群");
        groupChatService.update(groupChat);
    }

    /**
     * 通过群id删除群成员
     *
     * @param groupId 群id
     * @return 结果
     */
    public int deleteByGroupId(Long groupId) {
        log.info("delete by group id:{}", groupId);
        return groupChatMemberMapper.deleteByGroupId(groupId);
    }

    /**
     * 通过群id查询群成员数量
     *
     * @param groupIds 群id集合
     * @return 结果
     */
    public List<GroupMemberCountBO> selectGroupMemberCountByGroup(List<Long> groupIds) {
        return groupChatMemberMapper.selectGroupMemberCountByGroup(groupIds);
    }

    /**
     * 通过群主id查询群成员数量
     *
     * @param ownerIds 群主id集合
     * @return 结果
     */
    public List<GroupMemberCountBO> selectGroupMemberCountByOwner(List<Long> ownerIds) {
        return groupChatMemberMapper.selectGroupMemberCountByOwner(ownerIds);
    }

    /**
     * 通过群主查询成员人数
     *
     * @param ownerId 群主id
     * @return 结果
     */
    public int selectCountByOwner(Long ownerId) {
        if (ownerId == null) {
            return 0;
        }
        return groupChatMemberMapper.selectCountByOwner(ownerId);
    }


    /**
     * 统计群成员加入人数
     */
    public List<GroupStatisticsBO> countJoinMember(Collection<Long> groupIds, LocalDateTime startTime, LocalDateTime endTime) {
        return groupChatMemberMapper.countJoinMember(groupIds, startTime, endTime);
    }

    public List<GroupChatMember> selectByGroupId(Long groupId) {
        return null == groupId ? Collections.emptyList() : groupChatMemberMapper.selectByGroupId(groupId);
    }

}
