package com.guosen.ewas.wechat.msg.service;

import com.github.pagehelper.PageInfo;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.msg.domain.bo.CustomerSearchBO;
import com.guosen.ewas.wechat.msg.domain.bo.MessageBO;
import com.guosen.ewas.wechat.msg.domain.bo.MsgLastBO;
import com.guosen.ewas.wechat.msg.domain.entity.MsgLast;
import com.guosen.ewas.wechat.msg.domain.vo.MsgLastCustomerVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgLastGroupVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgLastUserVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgUserReqVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgPeopleReqVO;

import java.util.Set;

/**
 * 聊天记录最后一条消息记录表。
 * 当遇到性能瓶颈时可选的两个策略：
 * 1、启用缓存
 * 2、表拆分，可以按：外部聊天，内部聊天，群聊天拆分
 *
 * <AUTHOR> Peijun
 */
public interface IMsgLastService {
    /**
     * <p>
     * 记录可确定发送接收对象之间的最后一条消息
     * 数据库记录的内容为
     * 发送人和接收人：
     * fromId为员工小id，receiveId为员工大id （员工发送给员工）
     * fromId为员工id,receiveId为客户id （客户发送给员工或员工发送给客户）
     * fromId为发送人，receiveId为群id (群消息)
     *
     * @param msgData 消息内容
     */
    void msgLastLog(MessageBO msgData);

    /**
     * 根据关系获取最后一条消息
     *
     * @param relation 会话关系
     * @return 最后一条聊天记录
     */
    MsgLast getByRelation(String relation);

    /**
     * 根据查询条件获取所有会话中的开始时间和结束时间
     *
     * @param msgLast 查询条件
     * @return 组合后的数据
     */
    MsgLast getMaxAndMinTime(MsgLastBO msgLast);

    /**
     * 员工检索-客户列表-分页
     *
     * @param page 请求
     * @return 未删除的客户列表
     */
    PageInfo<MsgLastCustomerVO> pageCustomerByUser(PageWrapVO<MsgPeopleReqVO> page);

    /**
     * 员工检索-员工列表-分页
     *
     * @param page 请求
     * @return 未删除的客户列表
     */
    PageInfo<MsgLastUserVO> pageUserByUser(PageWrapVO<MsgPeopleReqVO> page);

    /**
     * 客户检索-员工列表-分页
     *
     * @param page 条件
     * @return 结果
     */
    PageInfo<MsgLastUserVO> pageUserByCustomer(PageWrapVO<MsgPeopleReqVO> page);


    /**
     * 员工检索-群列表-分页
     *
     * @param pageReqVO 条件
     * @return 结果
     */
    PageInfo<MsgLastGroupVO> pageGroupByUser(PageWrapVO<MsgPeopleReqVO> pageReqVO);

    /**
     * 客户检索-群列表-分页
     *
     * @param pageReqVO 条件
     * @return 结果
     */
    PageInfo<MsgLastGroupVO> pageGroupByCustomer(PageWrapVO<MsgPeopleReqVO> pageReqVO);

    /**
     * 员工检索-分页
     *
     * @param page 请求
     */
    PageInfo<MsgLastUserVO> pageUser(PageWrapVO<MsgUserReqVO> page);

    /**
     * 获取客户 ID 集合
     */
    Set<Long> selectCustomerIdsByBO(CustomerSearchBO searchBO);

}
