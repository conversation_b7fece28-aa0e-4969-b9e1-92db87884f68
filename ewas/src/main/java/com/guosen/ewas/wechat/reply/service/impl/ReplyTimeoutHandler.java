package com.guosen.ewas.wechat.reply.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.constant.RedisKeyConsts;
import com.guosen.ewas.wechat.common.redis.RedisDistributedLockHelper;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.common.util.DateUtils;
import com.guosen.ewas.wechat.common.util.JacksonUtils;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import com.guosen.ewas.wechat.customer.domain.entity.Customer;
import com.guosen.ewas.wechat.customer.service.CustomerService;
import com.guosen.ewas.wechat.msg.domain.bo.MessageBO;
import com.guosen.ewas.wechat.msg.enums.ChatType;
import com.guosen.ewas.wechat.msg.enums.MsgEventType;
import com.guosen.ewas.wechat.msg.enums.SendTypeEnum;
import com.guosen.ewas.wechat.msg.event.MsgEvent;
import com.guosen.ewas.wechat.reply.domain.bo.ReplyTimeoutMsgCacheBO;
import com.guosen.ewas.wechat.reply.domain.bo.ReplyTimeoutRecordBO;
import com.guosen.ewas.wechat.reply.domain.po.ReplyTimeoutRecord;
import com.guosen.ewas.wechat.reply.domain.po.ReplyTimeoutSet;
import com.guosen.ewas.wechat.reply.domain.po.ReplyTimeoutStatsDay;
import com.guosen.ewas.wechat.reply.service.ReplyTimeoutRecordService;
import com.guosen.ewas.wechat.reply.service.ReplyTimeoutService;
import com.guosen.ewas.wechat.reply.service.ReplyTimeoutStatsDayService;
import com.guosen.ewas.wechat.uc.domain.bo.ScopeBO;
import com.guosen.ewas.wechat.uc.domain.po.Department;
import com.guosen.ewas.wechat.uc.domain.po.User;
import com.guosen.ewas.wechat.uc.service.DepartmentService;
import com.guosen.ewas.wechat.uc.service.UserService;
import com.guosen.ewas.wechat.weixin.cp.ICpMessagePushService;
import com.guosen.ewas.wechat.weixin.cp.constant.MsgTemplate;
import com.guosen.ewas.wechat.weixin.cp.constant.MsgType;
import com.guosen.ewas.wechat.weixin.cp.dto.message.TextMessageReq;
import com.guosen.zebra.distributed.lock.DistributedLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 回复超时处理业务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReplyTimeoutHandler {
    /**
     * 用于标识消息已处理
     */
    private static final String MSG_ID_END = "0";

    private final ReplyTimeoutService replyTimeoutService;
    private final UserService userService;
    private final DepartmentService departmentService;
    private final CustomerService customerService;
    private final ICpMessagePushService iCpMessagePushService;
    private final ReplyTimeoutRecordService replyTimeoutRecordService;
    private final ReplyTimeoutStatsDayService replyTimeoutStatsDayService;
    private final ConfigKvService configKvService;

    @EventListener
    public void receiveMessage(MsgEvent msgEvent) {
        // 为空、非消息事件、补充消息，不进行处理
        if (null == msgEvent || !MsgEventType.MSG_EVENT.equals(msgEvent.getMsgEventType())) {
            log.info("Receive message is null or type not MSG_EVENT {}", msgEvent);
            return;
        }
        // 集群情况，消息不一定是顺序接受，因此需要自行考虑消息的先后
        MessageBO send = msgEvent.getData();
        if (null == send || send.getCompensation() || !ChatType.SINGLE.equals(send.getChatTypeEnum())) {
            // 不为单聊的场景，无需考虑
            return;
        }
        // 数据完整性校验
        if (null == send.getReceiveId() || null == send.getFromId() || null == send.getMsgTimestamp()) {
            log.info("Receive message is null. {}", msgEvent);
            return;
        }
        if (SendTypeEnum.CUSTOMER_TO_USER.equals(send.getSendTypeEnum()) ||
                SendTypeEnum.USER_TO_CUSTOMER.equals(send.getSendTypeEnum())) {
            ReplyTimeoutMsgCacheBO cache = new ReplyTimeoutMsgCacheBO();
            if (SendTypeEnum.CUSTOMER_TO_USER.equals(send.getSendTypeEnum())) {
                cache.setUserId(send.getReceiveId());
                cache.setCustomerId(send.getFromId());
            } else {
                cache.setUserId(send.getFromId());
                cache.setCustomerId(send.getReceiveId());
            }
            cache.setMsgId(send.getMsgId());
            cache.setDatetime(DateUtils.ms2Time(send.getMsgTimestamp()));
            cache.setSendType(send.getSendTypeEnum().getCode());
            cache.setMsgContent(StrUtil.isBlank(send.getContent()) ? send.getMsgTypeEnum().getExplain() : send.getContent());
            if (checkIsEnabled(cache.getUserId(), cache.getDatetime())) {
                RedisUtils.addListLeft(RedisKeyConsts.ReplyTimeout.MSG_QUEUE, JacksonUtils.obj2Json(cache));
            }
        }
        // 其它发送场景，不需要处理
    }

    /**
     * 回复超时定时处理器：<br/>
     * 场景：集群-定时器调用 <br/>
     * 1、集群-各机器同时启动定时器<br/>
     * 2、定时器从Set中获取用户id和客户id组合成的hashKey<br/>
     * 3、根据hashKey从缓存中获取单条数据<br/>
     * 4、处理单条数据加锁：因为可能其中一个任务刚取出hashKey,消息接收了新的消息，另一个任务也能取到相同的消息key<br/>
     */
    public void replyTimeoutJobHandler() {
        log.trace("Reply timeout Job start.");
        String key = RedisKeyConsts.ReplyTimeout.MSG_SET_HASHKEY;
        // 方法为定时器调用，因此只要处理当前长度数据即可。
        Long size = RedisUtils.getSetSize(key);
        log.debug("Reply timeout Job  hash key set size={}", size);
        for (int i = 0; i < size; i++) {
            Object cache = RedisUtils.popSet(key);
            if (null == cache) {
                // 说明队列没数据了，直接结束循环
                log.debug("Cache hash key is empty end while. key={}", key);
                break;
            }
            messageOut(cache.toString());
        }
        log.trace("Reply timeout Job end.");
    }

    /**
     * 定时器调用：
     * 汇总每日回复超时记录
     */
    public void replyTimeoutStatsJobHandler() {
        // 获取昨日数据
        LocalDate yesterday = LocalDate.now().minusDays(1);
        ReplyTimeoutRecordBO param = new ReplyTimeoutRecordBO();
        param.setStartDatetime(DateUtils.getMin(yesterday));
        param.setEndDatetime(DateUtils.getMax(yesterday));
        List<ReplyTimeoutRecordBO> records = replyTimeoutRecordService.selectByBo(param);
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, List<ReplyTimeoutRecordBO>> map =
                    records.stream().collect(Collectors.groupingBy(ReplyTimeoutRecordBO::getUserId));
            // 统计数据量不大，且在非高峰时段，可以单条新增插入。
            map.forEach((k, v) -> {
                ReplyTimeoutStatsDay day = new ReplyTimeoutStatsDay();
                day.setUserId(k);
                day.setQuantity(v.size());
                day.setStatsDate(yesterday);
                day.setIsDelete(BusinessConsts.NOT_DELETE);
                day.setCreateDatetime(LocalDateTime.now());
                day.setRemark("system");
                replyTimeoutStatsDayService.insertSelective(day);
            });
        }
    }

    /**
     * 消息队列里消费消息。
     * 如果消息锁住则丢会消息处理队列
     */
    public void popMsg() {
        // 这里消息会遇到获取锁失败，因此需要无线循环消费消息
        while (true) {
            Object obj = RedisUtils.popListRight(RedisKeyConsts.ReplyTimeout.MSG_QUEUE);
            if (obj == null) {
                log.debug("Reply timeout msg  queue no data。");
                break;
            }
            messageIn(JacksonUtils.json2Obj(obj.toString(), ReplyTimeoutMsgCacheBO.class));
        }
    }

    /**
     * 消息监听事件调用此消息进入处理方法。<br/>
     * 员工与客户之间的消息处理<br/>
     * 集群，多线程场景并非按顺序接受到消息<br/>
     * 采用缓存用户交互的最后一条消息<br/>
     * 通过对比消息时间和类型来，进而判断是否需要提醒。<br/>
     * 发送过超时提醒的消息，则改变消息发送类型、时间、id，来避免重复推送。<br/>
     * 消息接受和消息处理，针对同一会话，需要加相同锁处理。
     *
     * @param cacheBO 消息
     */
    private void messageIn(ReplyTimeoutMsgCacheBO cacheBO) {
        // 员工ID-客户ID
        String lockKey = getLockKey(cacheBO.getUserId(), cacheBO.getCustomerId());
        DistributedLock lock = RedisDistributedLockHelper.getLock(lockKey, 10L);
        if (null == lock || !lock.tryLock()) {
            // 获取不到锁，丢回消息队列。
            RedisUtils.addListLeft(RedisKeyConsts.ReplyTimeout.MSG_QUEUE, JacksonUtils.obj2Json(cacheBO));
            log.debug("Reply Timeout receive message add cache get lock fail. lock key={}", lockKey);
            return;
        }
        try {
            String key = StrUtil.format(RedisKeyConsts.ReplyTimeout.MSG_LAST, cacheBO.getUserId(), cacheBO.getCustomerId());
            String preMsg = RedisUtils.getStr(key);
            if (StrUtil.isBlank(preMsg)) {
                // 缓存不存在，直接新增
                addMsgCache(key, cacheBO);
                return;
            }
            // 后来的消息则更新，之前消息直接丢弃
            ReplyTimeoutMsgCacheBO message = JacksonUtils.json2Obj(preMsg, ReplyTimeoutMsgCacheBO.class);
            if (cacheBO.getDatetime().isAfter(message.getDatetime())) {
                addMsgCache(key, cacheBO);
            }
        } finally {
            lock.unlock();
        }
    }

    private void addMsgCache(String key, ReplyTimeoutMsgCacheBO value) {
        log.trace("Add msg cache key={},{}", key, value);
        long timeout = RedisKeyConsts.ReplyTimeout.TIMEOUT;
        RedisUtils.addStr(key, JacksonUtils.obj2Json(value), timeout);
        if (SendTypeEnum.CUSTOMER_TO_USER.getCode().equals(value.getSendType())) {
            String hashKey = value.getUserId() + RedisKeyConsts.ReplyTimeout.ID_SPLIT + value.getCustomerId();
            // 客户消息，则都需要添加到set集合
            RedisUtils.addInSet(RedisKeyConsts.ReplyTimeout.MSG_SET_HASHKEY, timeout, hashKey);
        }
    }

    /**
     * 定时器调用此消息处理方法。<br/>
     * 数据的处理逻辑与messageIn()相呼应
     *
     * @param hashKey 缓存的hashKey
     */
    private void messageOut(String hashKey) {
        String[] ids = hashKey.split(RedisKeyConsts.ReplyTimeout.ID_SPLIT);
        // 针对单条消息加锁
        DistributedLock lock = RedisDistributedLockHelper.getLock(getLockKey(ids[0], ids[1]), 10L);
        if (lock == null || !lock.tryLock()) {
            RedisUtils.addInSet(RedisKeyConsts.ReplyTimeout.MSG_SET_HASHKEY, RedisKeyConsts.ReplyTimeout.TIMEOUT, hashKey);
            log.info("Get lock(key = {}) fail. skip job. {}", hashKey, lock);
            return;
        }
        try {
            ReplyTimeoutSet setting = getReplyTimeoutSet();
            String key = StrUtil.format(RedisKeyConsts.ReplyTimeout.MSG_LAST, ids[0], ids[1]);
            String value = RedisUtils.getStr(key);
            if (StrUtil.isBlank(value)) {
                log.debug("Reply timeout cache msg is null.");
                return;
            }
            ReplyTimeoutMsgCacheBO cache = JacksonUtils.json2Obj(value, ReplyTimeoutMsgCacheBO.class);
            log.trace("Reply timeout cache msg {}.", cache);
            if (!SendTypeEnum.CUSTOMER_TO_USER.getCode().equals(cache.getSendType())) {
                log.trace("Reply timeout cache msg is not customer to user.");
                // 缓存的消息非客户发来的消息，直接跳过,也无需再把hashKey加回Set
                return;
            }
            if (!isEnableSetting(setting, cache.getDatetime())) {
                // 数据不在检测范围，直接跳过,也无需再把hashKey加回Set
                log.trace("Reply timeout cache msg is not customer to user.");
                return;
            }
            LocalDateTime now = LocalDateTime.now();
            Duration dur = Duration.between(cache.getDatetime(), now);
            long diffTime = dur.toMinutes();
            // 检测是否超时
            if (diffTime >= setting.getTimeout()) {
                // 此时不能移除消息了，否则后来消息会重复发送提醒，采用占位消息处理
                ReplyTimeoutMsgCacheBO upd = new ReplyTimeoutMsgCacheBO();
                upd.setUserId(cache.getUserId());
                upd.setCustomerId(cache.getCustomerId());
                upd.setMsgId(MSG_ID_END);
                upd.setDatetime(now);
                upd.setSendType(SendTypeEnum.USER_TO_CUSTOMER.getCode());
                addMsgCache(key, upd);
                // 新增记录 和 消息推送
                recordAndPushMsg(cache, now, diffTime);
            } else {
                // 未超时，加回队列
                RedisUtils.addInSet(RedisKeyConsts.ReplyTimeout.MSG_SET_HASHKEY, RedisKeyConsts.ReplyTimeout.TIMEOUT, hashKey);
            }
        } catch (Exception e) {
            log.error("Reply timeout msg handler exception ", e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 记录并推送超时提醒,建议异步调用此方法
     *
     * @param cache       缓存消息
     * @param triggerDate 触发时间
     * @param diffTime    实际超时时间
     */
    private void recordAndPushMsg(ReplyTimeoutMsgCacheBO cache, LocalDateTime triggerDate, long diffTime) {
        ReplyTimeoutRecord addRecord = new ReplyTimeoutRecord();
        addRecord.setUserId(cache.getUserId());
        List<Department> departments = userService.selectDepartmentById(addRecord.getUserId());
        if (CollUtil.isNotEmpty(departments)) {
            addRecord.setUserDepartment(
                    departments.stream().map(Department::getName).collect(Collectors.joining(",")));
        }
        addRecord.setCustomerId(cache.getCustomerId());
        addRecord.setMsgId(cache.getMsgId());
        addRecord.setMsgContent(cache.getMsgContent());
        addRecord.setTriggerDatetime(triggerDate);
        addRecord.setIsDelete(BusinessConsts.NOT_DELETE);
        addRecord.setCreateDatetime(LocalDateTime.now());
        log.debug("Message handler send record. {}", addRecord);
        if (BusinessConsts.DB_SUCCESS.equals(replyTimeoutRecordService.insertSelective(addRecord))) {
            String openFlag = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SYSTEM, "REPLY_TIMEOUT_NOTIFIER");
            if ("false".equalsIgnoreCase(openFlag)) {
                log.info("Push replay timeout message. notifier is close. {}", openFlag);
                return;
            }
            // 消息推送
            User user = userService.selectByPrimaryKey(cache.getUserId());
            Customer customer = customerService.selectOneById(cache.getCustomerId());
            if (null == user || customer == null) {
                log.warn("Push replay timeout message. user is null. {}", cache);
                return;
            }
            if (StrUtil.isNotBlank(customer.getName()) && customer.getName().contains("未加企微好友")
                    && customer.getName().contains(customer.getExternalUserid())) {
                log.info("Push replay timeout message. customer is ignore. {},{}", cache, customer);
                return;
            }
            TextMessageReq messageReq = new TextMessageReq();
            messageReq.setToUser(user.getUserid());
            messageReq.setMsgType(MsgType.TEXT);
            TextMessageReq.TextDTO text = new TextMessageReq.TextDTO();
            String content = StrUtil.format(MsgTemplate.REPLY_TIMEOUT_WARN, customer.getName(), diffTime);
            text.setContent(content);
            messageReq.setText(text);
            messageReq.setEnableDuplicateCheck(BusinessConsts.ENABLE);
            log.debug("Push message {},{}", messageReq, cache);
            iCpMessagePushService.pushMessage(messageReq);
        } else {
            log.error("Record write to db fail.{}", addRecord);
        }
    }

    /**
     * 检测用户消息是否需要进行超时回复处理
     */
    private boolean checkIsEnabled(Long userId, LocalDateTime msgTime) {
        LocalDateTime now = null == msgTime ? LocalDateTime.now() : msgTime;
        ReplyTimeoutSet setting = getReplyTimeoutSet();
        return isEnableSetting(setting, now) && checkUserInScope(setting, userId);
    }

    /**
     * 检测当前时间是否开启设置
     *
     * @param now 默认取当前时间
     * @return true -> 需要执行监控 <br/>
     * false -> 不需要执行监控<br/>
     */
    private boolean isEnableSetting(ReplyTimeoutSet setting, LocalDateTime now) {
        now = null == now ? LocalDateTime.now() : now;
        setting = null == setting ? getReplyTimeoutSet() : setting;
        // 检测时间，检测星期，检测时间段，检测数据范围
        if (setting.getTimeout() <= 0) {
            return Boolean.FALSE;
        }
        if (CollUtil.isEmpty(setting.getWeeks()) || !setting.getWeeks().contains(now.getDayOfWeek().getValue())) {
            return Boolean.FALSE;
        }
        Integer time = DateUtils.parseTime2Second(now);
        if (time < setting.getStartTimeVal() || time > setting.getEndTimeVal()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 仅仅检测员工当前是否属于回复超时监管范围
     *
     * @param userId - 员工ID
     * @return true -> 需要执行监控 <br/>
     * false -> 不需要执行监控<br/>
     */
    private boolean checkUserInScope(ReplyTimeoutSet setting, Long userId) {
        // 范围匹配，正向匹配更快,范围为空，默认监控所有
        ScopeBO scope = setting.getScope();
        if (null == scope) {
            return Boolean.TRUE;
        }
        if (CollUtil.isEmpty(scope.getUserIds()) && CollUtil.isEmpty(scope.getDepartmentIds())) {
            return Boolean.TRUE;
        }
        if (CollUtil.isNotEmpty(scope.getUserIds()) && scope.getUserIds().contains(userId)) {
            return Boolean.TRUE;
        }
        Set<Long> userIds = departmentService.childrenUserByDepartmentId(scope.getDepartmentIds(), false);
        if (CollUtil.isNotEmpty(userIds) && userIds.contains(userId)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private ReplyTimeoutSet getReplyTimeoutSet() {
        return replyTimeoutService.getReplyTimeoutSet();
    }

    private String getLockKey(Object userId, Object customerId) {
        return StrUtil.format(RedisKeyConsts.ReplyTimeout.LOCK_MSG, userId, customerId);
    }
}