package com.guosen.ewas.wechat.msg.domain.wrap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChatDataWrap {
    @JsonProperty("seq")
    private Integer seq;
    @JsonProperty("msgid")
    private String msgid;
    @JsonProperty("publickey_ver")
    private Integer publickeyVer;
    @JsonProperty("encrypt_random_key")
    private String encryptRandomKey;
    @JsonProperty("encrypt_chat_msg")
    private String encryptChatMsg;

}
