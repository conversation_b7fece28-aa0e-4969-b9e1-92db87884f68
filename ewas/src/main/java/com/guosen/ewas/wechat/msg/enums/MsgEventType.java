package com.guosen.ewas.wechat.msg.enums;

/**
 * 消息事件类型枚举
 * <AUTHOR>
 */
public enum MsgEventType {
    /**
     * 消息事件
     */
    MSG_EVENT(1, "msg_event"),
    /**
     * 媒体消息事件
     */
    MSG_MEDIA_EVENT(7, "msg_media_event"),
    /**
     * 同步
     */
    SYNC(2, "sync"),
    /**
     * 同步群
     */
    SYNC_GROUP(3, "sync_group"),
    /**
     * 消息拉取错误
     */
    MSG_ERROR(4, "msg_error"),
    /**
     * 媒体消息拉取错误
     */
    MSG_MEDIA_ERROR(5, "msg_media_error"),
    /**
     * 撤回消息错误
     */
    RECALL_ERROR(6, "recall_error");
    private final Integer code;
    private final String type;

    MsgEventType(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
