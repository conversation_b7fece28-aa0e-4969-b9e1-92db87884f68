package com.guosen.ewas.wechat.weixin.cp.dto.ContactWay;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 联系我 ，通用请求参数
 *
 * <AUTHOR>
 * @date 2021/1/13 11:41
 */
public class ContactWayReq {

    /**
     * 联系方式类型,1-单人, 2-多人
     */
    private Integer type;

    /**
     * 在小程序中联系时使用的控件样式，详见附表
     */
    private Integer style;

    /**
     * 联系方式的备注信息，用于助记，不超过30个字符
     */
    private String remark;

    /**
     * 外部客户添加时是否无需验证，默认为true
     */
    @JsonProperty("skip_verify")
    private Boolean skipVerify;

    /**
     * 企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详情”时会返回该参数值，不超过30个字符
     */
    private String state;

    /**
     * 临时会话二维码有效期，以秒为单位。该参数仅在is_temp为true时有效，默认7天
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;
    /**
     * 临时会话有效期，以秒为单位。该参数仅在is_temp为true时有效，默认为添加好友后24小时
     */
    @JsonProperty("chat_expires_in")
    private Integer chatExpiresIn;
    /**
     * 可进行临时会话的客户unionid，该参数仅在is_temp为true时有效，如不指定则不进行限制
     *  使用unionid需要调用方（企业或服务商）的企业微信“客户联系”中已绑定微信开发者账户
     */
    private String unionid;
    /**
     * 	结束语，会话结束时自动发送给客户，可参考“结束语定义”，仅在is_temp为true时有效
     */
    private ConclusionsDTO conclusions;
    /**
     * 使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
     */
    private List<String> user;
    /**
     * 使用该联系方式的部门id列表，只在type为2时有效
     */
    private List<Long> party;

    /**
     * 结束语消息体
     */
    public static class ConclusionsDTO {

        private TextDTO text;
        private ImageDTO image;
        private LinkDTO link;
        private TextDTO textDTO;
        private ImageDTO imageDTO;
        private LinkDTO linkDTO;
        private MiniprogramDTO miniprogramDTO;

        /**
         *  文本消息
         */
        public static class TextDTO {
            /**
             * content : 文本消息内容
             */
            private String content;

            public String getContent() {
                return content;
            }

            public void setContent(String content) {
                this.content = content;
            }
        }

        /**
         *  图片消息
         */
        public static class ImageDTO {
            /**
             * 图片的media_id
             */
            @JsonProperty("media_id")
            private String mediaId;

            public String getMediaId() {
                return mediaId;
            }

            public void setMediaId(String mediaId) {
                this.mediaId = mediaId;
            }
        }

        /**
         *  图文消息
         */
        public static class LinkDTO {
            /**
             * 图文消息标题，最长为128字节
             */
            private String title;
            /**
             * 图文消息封面的url
             */
            @JsonProperty("picurl")
            private String picUrl;
            /**
             *	图文消息的描述，最长为512字节
             */
            private String desc;
            /**
             * 图文消息的链接
             */
            private String url;

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public String getUrl() {
                return url;
            }

            public void setUrl(String url) {
                this.url = url;
            }
        }

        // 小程序
        public static class MiniprogramDTO {

            /**
             * 消息标题
             */
            private String title;
            private String picMediaId;
            private String appid;
            private String page;

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getPicMediaId() {
                return picMediaId;
            }

            public void setPicMediaId(String picMediaId) {
                this.picMediaId = picMediaId;
            }

            public String getAppid() {
                return appid;
            }

            public void setAppid(String appid) {
                this.appid = appid;
            }

            public String getPage() {
                return page;
            }

            public void setPage(String page) {
                this.page = page;
            }
        }


        public TextDTO getText() {
            return text;
        }

        public void setText(TextDTO text) {
            this.text = text;
        }

        public ImageDTO getImage() {
            return image;
        }

        public void setImage(ImageDTO image) {
            this.image = image;
        }

        public LinkDTO getLink() {
            return link;
        }

        public void setLink(LinkDTO link) {
            this.link = link;
        }

        public TextDTO getTextDTO() {
            return textDTO;
        }

        public void setTextDTO(TextDTO textDTO) {
            this.textDTO = textDTO;
        }

        public ImageDTO getImageDTO() {
            return imageDTO;
        }

        public void setImageDTO(ImageDTO imageDTO) {
            this.imageDTO = imageDTO;
        }

        public LinkDTO getLinkDTO() {
            return linkDTO;
        }

        public void setLinkDTO(LinkDTO linkDTO) {
            this.linkDTO = linkDTO;
        }

        public MiniprogramDTO getMiniprogramDTO() {
            return miniprogramDTO;
        }

        public void setMiniprogramDTO(MiniprogramDTO miniprogramDTO) {
            this.miniprogramDTO = miniprogramDTO;
        }

        @Override
        public String toString() {
            return "ConclusionsDTO{" +
                    "text=" + text +
                    ", image=" + image +
                    ", link=" + link +
                    ", textDTO=" + textDTO +
                    ", imageDTO=" + imageDTO +
                    ", linkDTO=" + linkDTO +
                    ", miniprogramDTO=" + miniprogramDTO +
                    '}';
        }
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStyle() {
        return style;
    }

    public void setStyle(Integer style) {
        this.style = style;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getSkipVerify() {
        return skipVerify;
    }

    public void setSkipVerify(Boolean skipVerify) {
        this.skipVerify = skipVerify;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn) {
        this.expiresIn = expiresIn;
    }

    public Integer getChatExpiresIn() {
        return chatExpiresIn;
    }

    public void setChatExpiresIn(Integer chatExpiresIn) {
        this.chatExpiresIn = chatExpiresIn;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public ConclusionsDTO getConclusions() {
        return conclusions;
    }

    public void setConclusions(ConclusionsDTO conclusions) {
        this.conclusions = conclusions;
    }

    public List<String> getUser() {
        return user;
    }

    public void setUser(List<String> user) {
        this.user = user;
    }

    public List<Long> getParty() {
        return party;
    }

    public void setParty(List<Long> party) {
        this.party = party;
    }

    @Override
    public String toString() {
        return "ContactWayReq{" +
                "type=" + type +
                ", style=" + style +
                ", remark='" + remark + '\'' +
                ", skipVerify=" + skipVerify +
                ", state='" + state + '\'' +
                ", expiresIn=" + expiresIn +
                ", chatExpiresIn=" + chatExpiresIn +
                ", unionid='" + unionid + '\'' +
                ", conclusions=" + conclusions +
                ", user=" + user +
                ", party=" + party +
                '}';
    }
}
