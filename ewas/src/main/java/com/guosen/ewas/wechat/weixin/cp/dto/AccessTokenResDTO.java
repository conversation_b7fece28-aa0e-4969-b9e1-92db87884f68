package com.guosen.ewas.wechat.weixin.cp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 基本的微信接收参数
 *
 * <AUTHOR>
 * @date 2020/12/28 13:55
 */
public class AccessTokenResDTO extends BaseWxDTO{

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("expires_in")
    private Long expiresIn;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    @Override
    public String toString() {
        return "AccessTokenResDTO{" +
                "accessToken='" + accessToken + '\'' +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
