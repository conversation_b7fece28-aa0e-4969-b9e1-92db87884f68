package com.guosen.ewas.wechat.sensitive.domain.bo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 敏感行为触发记录承载实体
 *
 * <AUTHOR>
 * @date 2021/9/6 20:11
 */
public class SensitiveBehaviorRecordBO implements Serializable {

    private static final long serialVersionUID = 6572217770415486783L;

    private Long id;

    private Long sendId;

    private String sendName;

    private Long receiveId;

    private String receiveName;

    private Integer sendType;

    private LocalDateTime createDatetime;

    public SensitiveBehaviorRecordBO() {
    }

    public SensitiveBehaviorRecordBO(Long id, Long sendId, String sendName, Long receiveId, String receiveName, Integer sendType, LocalDateTime createDatetime) {
        this.id = id;
        this.sendId = sendId;
        this.sendName = sendName;
        this.receiveId = receiveId;
        this.receiveName = receiveName;
        this.sendType = sendType;
        this.createDatetime = createDatetime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSendId() {
        return sendId;
    }

    public void setSendId(Long sendId) {
        this.sendId = sendId;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public Long getReceiveId() {
        return receiveId;
    }

    public void setReceiveId(Long receiveId) {
        this.receiveId = receiveId;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    @Override
    public String toString() {
        return "SensitiveBehaviorRecordBO{" +
                "id=" + id +
                ", sendId=" + sendId +
                ", sendName='" + sendName + '\'' +
                ", receiveId=" + receiveId +
                ", receiveName='" + receiveName + '\'' +
                ", sendType=" + sendType +
                ", createDatetime=" + createDatetime +
                '}';
    }
}
