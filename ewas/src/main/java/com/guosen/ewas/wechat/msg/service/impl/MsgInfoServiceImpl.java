package com.guosen.ewas.wechat.msg.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.constant.DBConsts;
import com.guosen.ewas.wechat.common.constant.DateConsts;
import com.guosen.ewas.wechat.common.util.DateUtils;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import com.guosen.ewas.wechat.msg.constant.MsgInfoConsts;
import com.guosen.ewas.wechat.msg.dao.MsgInfoMapper;
import com.guosen.ewas.wechat.msg.domain.bo.MsgInfoQueryBO;
import com.guosen.ewas.wechat.msg.domain.entity.MsgInfo;
import com.guosen.ewas.wechat.msg.service.IMsgInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话消息记录表。
 * 采用每月备份：
 * 示例：message_info_202109
 * 此业务只是作为业务数据备份，无其它实际业务作用
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MsgInfoServiceImpl implements IMsgInfoService {
    private static final String TABLE_PREFIX = MsgInfoConsts.TABLE_PREFIX;
    /**
     * 缓存的表名前缀
     */
    private static final String CACHE_TABLE = "msg:tableName:";
    /**
     * 缓存三个月的表名
     **/
    private static final Long CACHE_TABLE_TIMEOUT = 60 * 60 * 24 * 90L;
    private final MsgInfoMapper msgInfoMapper;

    @Override
    public List<MsgInfo> selectByMsgIds(List<String> msgIds) {
        if (CollUtil.isEmpty(msgIds)) {
            return Collections.emptyList();
        }
        List<MsgInfo> result = new ArrayList<>(msgIds.size());
        Map<String, List<String>> param = msgIds.stream().collect(Collectors.groupingBy(this::getTableByMsgId));
        param.forEach((k, v) -> {
            if (tableExists(k)) {
                result.addAll(msgInfoMapper.selectByMsgIds(v, k));
            }
        });
        // 只能代码去重复，有可能跨表查询
        return CollUtil.isNotEmpty(result) ? result.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MsgInfo::getMsgId))),
                        ArrayList::new)) : result;
    }

    @Override
    public MsgInfo getByMsgId(String msgId) {
        if (CharSequenceUtil.isNotBlank(msgId)) {
            String tableName = getTableByMsgId(msgId);
            if (tableExists(tableName)) {
                return msgInfoMapper.selectByMsgId(msgId, tableName);
            }
        }
        return null;
    }

    @Override
    public int insert(MsgInfo msgInfo) {
        if (null == msgInfo) {
            return BusinessConsts.DB_FAIL;
        }
        String table = getAndBuildTableByMsgId(msgInfo.getMsgId());
        if (StrUtil.isBlank(table)) {
            return BusinessConsts.DB_FAIL;
        }
        return msgInfoMapper.insert(msgInfo, table);
    }

    @Override
    public int addOrUpdRefactor(MsgInfo msgInfo) {
        if (null == msgInfo || StrUtil.isBlank(msgInfo.getMsgId())) {
            return DBConsts.FAIL;
        }
        if (null == getByMsgId(msgInfo.getMsgId())) {
            // 不存在,则新增数据
            return insert(msgInfo);
        } else {
            // 存在则更新
            return msgInfoMapper.updateByMsgId(msgInfo, getAndBuildTableByMsgId(msgInfo.getMsgId()));
        }
    }

    @Override
    public int addBatchMsgInfo(List<MsgInfo> records) {
        return CollUtil.isEmpty(records) ? BusinessConsts.DB_FAIL : insertBatch(records);
    }

    @Override
    public int updateByMsgId(MsgInfo msgInfo) {
        if (null == msgInfo) {
            return DBConsts.FAIL;
        }
        String table = getAndBuildTableByMsgId(msgInfo.getMsgId());
        if (StrUtil.isBlank(table)) {
            return DBConsts.FAIL;
        }
        return msgInfoMapper.updateByMsgId(msgInfo, table);
    }

    @Override
    public int updateBatchByMsgId(List<MsgInfo> records) {
        if (CollUtil.isEmpty(records)) {
            return DBConsts.FAIL;
        }
        int result = 0;
        for (MsgInfo info : records) {
            result += updateByMsgId(info);
        }
        return result;
    }

    @Override
    public List<MsgInfo> selectByQuery(MsgInfoQueryBO query) {
        log.debug("Msg info query {}", query);
        return null != query && tableExists(query.getTableName()) ? msgInfoMapper.selectByMsgInfoQueryBO(query) :
                Collections.emptyList();
    }

    @Override
    public List<MsgInfo> selectGlobalByQuery(MsgInfoQueryBO query) {
        return null != query && tableExists(query.getTableName()) ? msgInfoMapper.selectGlobalByMsgInfoQueryBO(query) : Collections.emptyList();
    }

    @Override
    public Long countGlobalByQuery(MsgInfoQueryBO query) {
        return null != query && tableExists(query.getTableName()) ? msgInfoMapper.countByMsgInfoQueryBO(query) : 0L;
    }

    @Override
    public boolean existsByMsgIds(List<String> msgIds) {
        if (CollUtil.isEmpty(msgIds)) {
            return false;
        }
        Map<String, List<String>> param = msgIds.stream().collect(Collectors.groupingBy(this::getTableByMsgId));
        for (Map.Entry<String, List<String>> entry : param.entrySet()) {
            if (tableExists(entry.getKey())) {
                if (null != msgInfoMapper.existsByMsgIds(entry.getValue(), entry.getKey())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void createTableByMonth(String month, Long idStart) {
        // 数据库创建语句做了IF NOT EXISTS 判断，此处可不加锁
        String tableName = TABLE_PREFIX + month;
        createTable(tableName, idStart);
        log.info("Create table={} success.", tableName);
        RedisUtils.add(CACHE_TABLE + tableName, tableName, CACHE_TABLE_TIMEOUT);
    }

    private int insertBatch(List<MsgInfo> records) {
        Map<String, List<MsgInfo>> param =
                records.stream().collect(Collectors.groupingBy(i -> getAndBuildTableByMsgId(i.getMsgId())));
        int result = 0;
        for (Map.Entry<String, List<MsgInfo>> entry : param.entrySet()) {
            result += msgInfoMapper.insertBatch(entry.getValue(), entry.getKey());
        }
        return result;
    }

    /**
     * 根据msgid获取表名，如果表不存在则创建表
     *
     * @param msgId 消息id
     * @return String
     */
    private String getAndBuildTableByMsgId(String msgId) {
        if (StrUtil.isBlank(msgId)) {
            return "";
        }
        String tableName = getTableByMsgId(msgId);
        if (!tableExists(tableName)) {
            createTableByName(tableName);
        }
        return tableName;
    }

    /**
     * msgId的结构里，包含了时间，因此从msgId里截取消息归属表
     * <p>
     * 示例值：<br/>
     * 13308860704189382034_1638325957257<br/>
     * 15524881768598775512_1638328719446_external<br/>
     *
     * @param msgId 微信消息唯一标识
     * @return String
     */
    private String getTableByMsgId(String msgId) {
        return TABLE_PREFIX + DateUtils.msgId2DateStr(msgId);
    }

    /**
     * 因数据库表：msg_info_*不纳入sql脚本整理范畴，
     * 并不知道客户具体上线部署时间，因此通过系统启动，来检测表是否已经存在，
     * 不存在则创建表
     *
     * @param tableName 表名
     * @return boolean
     */
    @Override
    public boolean tableExists(String tableName) {
        if (RedisUtils.exists(CACHE_TABLE + tableName)) {
            return true;
        }
        try {
            // 因为不确定账户是否具备，查看information_schema.tables权限
            // 因此采用捕获异常的方式,如果执行异常，说明表不存在，则新增表
            msgInfoMapper.getMinIdByTable(tableName);
            RedisUtils.add(CACHE_TABLE + tableName, tableName, CACHE_TABLE_TIMEOUT);
            return true;
        } catch (BadSqlGrammarException e) {
            log.info("tableExists BadSqlGrammarException ", e);
        }
        return false;
    }

    /**
     * 根据表名，创建表
     *
     * @param tableName 表名
     */
    private void createTableByName(String tableName) {
        // 数据库创建语句做了IF NOT EXISTS 判断，此处可不加锁
        createTable(tableName, getPreTableMaxId(tableName) + 10000000L);
        log.info("Create table={} success.", tableName);
        RedisUtils.add(CACHE_TABLE + tableName, tableName, CACHE_TABLE_TIMEOUT);
    }

    /**
     * 获取上一个月份表里数据的最大 id
     *
     * @param currentTable 当前表
     * @return 结果
     */
    private Long getPreTableMaxId(String currentTable) {
        // 获得日期字符串，示例：20220101
        String dateStr = currentTable.replace(TABLE_PREFIX, "") + "01";
        LocalDate localDate = DateUtils.str2Date(dateStr, DateConsts.FORMAT_YYYYMMDD);
        try {
            // 如果执行异常，说明表不存
            return msgInfoMapper.getMaxIdByTable(TABLE_PREFIX
                    + DateUtils.date2Str(localDate.minusMonths(1), DateConsts.FORMAT_YYYYMM));
        } catch (BadSqlGrammarException e) {
            log.info("tableExists BadSqlGrammarException ", e);
        }
        return 1L;
    }

    private void createTable(String tableName, Long idStart) {
        msgInfoMapper.createTable(tableName, null == idStart || idStart <= 0 ? 1L : idStart);
    }
}
