package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.LocationWrap;

/**
 * <AUTHOR>
 * @date 2021/12/29 16:12
 * 位置消息为：location。String类型
 */
public class LocationDTO extends DataBaseDTO {

    /**
     * location
     */
    @JsonProperty("location")
    private LocationWrap location;

    public LocationWrap getLocation() {
        return location;
    }

    public void setLocation(LocationWrap location) {
        this.location = location;
    }

    @Override
    public String toString() {
        return "LocationDTO{" +
                "location=" + location +
                '}';
    }
}
