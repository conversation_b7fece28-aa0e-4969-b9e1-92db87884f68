package com.guosen.ewas.wechat.reply.domain.vo;

import java.time.LocalDate;

/**
 * 回复超时记录
 *
 * <AUTHOR>
 */
public class ReplyTimeoutStatsDetailVO {

    /**
     * 统计日期
     * <p>
     * column: reply_timeout_stats_day.stats_date
     */
    private LocalDate statsDate;

    /**
     * 总人数
     */
    private Long totalUser;

    /**
     * 总次数
     */
    private Long totalQuantity;

    public LocalDate getStatsDate() {
        return statsDate;
    }

    public void setStatsDate(LocalDate statsDate) {
        this.statsDate = statsDate;
    }

    public Long getTotalUser() {
        return totalUser;
    }

    public void setTotalUser(Long totalUser) {
        this.totalUser = totalUser;
    }

    public Long getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Long totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    @Override
    public String toString() {
        return "ReplyTimeoutStatsSummaryVO{" + "statsDate=" + statsDate + ", totalUser=" + totalUser
                + ", totalQuantity=" + totalQuantity + '}';
    }
}