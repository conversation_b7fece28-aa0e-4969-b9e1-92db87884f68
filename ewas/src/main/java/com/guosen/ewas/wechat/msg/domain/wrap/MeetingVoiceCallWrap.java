package com.guosen.ewas.wechat.msg.domain.wrap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class MeetingVoiceCallWrap {
    /**
     * 音频结束时间。uint32类型
     */
    @JsonProperty("endtime")
    private Long endtime;
    /**
     * sdkfileid。音频媒体下载的id。String类型
     */
    @JsonProperty("sdkfileid")
    private String sdkFileId;
    /**
     * 文档分享对象，Object类型
     */
    @JsonProperty("demofiledata")
    private List<DemoFileDataWrap> demofiledata;
    /**
     * 屏幕共享对象，Object类型
     */
    @JsonProperty("sharescreendata")
    private List<SharescreendataWrap> sharescreendata;

}
