package com.guosen.ewas.wechat.msg.config;

import com.guosen.ewas.wechat.common.exception.ExceptionHandlingTaskDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 消息拉取，专用线程池、
 * 处理策略：如果达到最大值，就使用当前线程执行，不再开辟新线程
 *
 * <AUTHOR>
 */
@Slf4j
public class MsgPullThreadPool {

    /**
     * 核心线程数
     */
    private static final int CORE_POOL_SIZE = 2;

    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 5;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "Msg-pull-Thread-";
    private static ThreadPoolTaskExecutor executor = null;

    public static Executor getAsyncExecutor() {
        if (executor != null) {
            return executor;
        }
        executor = generate();
        return executor;
    }

    private static ThreadPoolTaskExecutor generate() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        // CallerRunsPolicy 如果达到最大值，就使用当前线程执行，不再开辟新线程
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        executor.setQueueCapacity(0);
        // 这是一种非常简洁的方法，可以让您更方便地为线程池中的所有任务添加异常处理。
        executor.setTaskDecorator(new ExceptionHandlingTaskDecorator());
        executor.initialize();
        log.info("消息处理-异步线程池 ThreadPoolTaskExecutor 初始化成功，核心线程数：{}，最大线程数：{}", CORE_POOL_SIZE, MAX_POOL_SIZE);
        return executor;
    }
}
