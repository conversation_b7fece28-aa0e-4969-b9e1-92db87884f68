package com.guosen.ewas.wechat.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.dao.ConfigKvMapper;
import com.guosen.ewas.wechat.common.domain.po.ConfigKv;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * the service implement of ConfigKv
 *
 * <AUTHOR>
 * @date 2021-09-03 19:29:16.0756
 */
@Service("configKvService")
@RequiredArgsConstructor
public class ConfigKvServiceImpl implements ConfigKvService {

    private final static Logger log = LoggerFactory.getLogger(ConfigKvServiceImpl.class);

    private final ConfigKvMapper configKvMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        log.debug("Delete by id = {}", id);
        ConfigKv record = new ConfigKv();
        record.setId(id);
        record.setIsDelete(BusinessConsts.IS_DELETE);
        record.setModifyDatetime(LocalDateTime.now());
        return updateByPrimaryKeySelective(record);
    }

    @Override
    public ConfigKv selectByPrimaryKey(Integer id) {
        log.info("Get by id = {}", id);
        return null == id ? null : configKvMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ConfigKv> selectByType(String type) {
        ConfigKv configKv = new ConfigKv();
        configKv.setCType(type);
        return configKvMapper.selectSelective(configKv);
    }

    @Override
    public ConfigKv getByTypeAndKey(String type, String key) {
        return configKvMapper.getByTypeAndKey(type, key);
    }

    @Override
    public String getValueByTypeAndKey(String type, String key) {
        return Optional.ofNullable(getByTypeAndKey(type, key)).orElse(new ConfigKv()).getCValue();
    }

    @Override
    public String getCacheValueByTypeAndKey(String type, String key, String defVal) {
        String cache = RedisUtils.getStr("conf:kv:" + type + ":" + key);
        if (StrUtil.isNotBlank(cache)) {
            return cache;
        }
        String value = getValueByTypeAndKey(type, key);
        if (StrUtil.isNotBlank(value)) {
            RedisUtils.addStr("conf:kv:" + type + ":" + key, value, 60);
            return value;
        }
        return defVal;
    }

    @Override
    public int updateByPrimaryKeySelective(ConfigKv record) {
        log.info("Update selective by id. {}", record);
        return null == record || null == record.getId() ? BusinessConsts.DB_FAIL
                : configKvMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int insertSelective(ConfigKv record) {
        return null == record ? BusinessConsts.DB_FAIL : configKvMapper.insertSelective(record);
    }

    @Override
    public int deletePhysicalByTypeAndKey(String type, String key) {
        return configKvMapper.deleteByCTypeAndCKey(type, key);
    }

}