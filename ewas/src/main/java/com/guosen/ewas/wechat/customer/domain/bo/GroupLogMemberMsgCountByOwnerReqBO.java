package com.guosen.ewas.wechat.customer.domain.bo;

import java.time.LocalDate;

/**
 * 群聊记录数据统计-按群主
 *
 * <AUTHOR>
 * @date 2021/9/11 16:04
 */
public class GroupLogMemberMsgCountByOwnerReqBO {
    /**
     * 群主id集合
     */
    private Long ownerId;

    /**
     * 昨天
     */
    private LocalDate yesterday;

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public LocalDate getYesterday() {
        return yesterday;
    }

    public void setYesterday(LocalDate yesterday) {
        this.yesterday = yesterday;
    }

    @Override
    public String toString() {
        return "GroupLogMemberMsgCountByOwnerReqBO{" +
                "ownerId=" + ownerId +
                ", yesterday=" + yesterday +
                '}';
    }
}
