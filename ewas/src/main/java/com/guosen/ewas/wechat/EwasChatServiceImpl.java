package com.guosen.ewas.wechat;

import com.guosen.commonsrv.zebra.ZebraServiceBase;
import com.guosen.commonsrv.zebra.interceptor.GrpcResponse;
import com.guosen.ewas.wechat.common.exception.BusinessException;
import com.guosen.ewas.wechat.common.util.FastJsonUtils;
import com.guosen.ewas.wechat.model.ewasmodelproto.ApiRequest;
import com.guosen.ewas.wechat.model.ewasmodelproto.ApiResponse;
import com.guosen.ewas.wechat.zebra.MsgGrpcRequest;
import com.guosen.jxhaccountsrv.contracts.CommonAppException;
import com.guosen.jxhcommon.contracts.ResponseContract;
import com.guosen.zebra.core.grpc.anotation.ZebraService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;

/**
 * Zebra微服务实现样例，请根据实际情况修改PB文件和此类
 *
 * <AUTHOR>
 */
@ZebraService
public class EwasChatServiceImpl extends ZebraServiceBase implements MsgService {

    private static final Logger LOG = LoggerFactory.getLogger(EwasChatServiceImpl.class);

    @Override
    public ApiResponse callService(ApiRequest apiRequest) {
        ApiResponse rsp = new ApiResponse();
        try {
            rsp.setResult(resultAsList(ok()));
            MsgGrpcRequest request = new MsgGrpcRequest(apiRequest);
            GrpcResponse<ApiResponse> response = new GrpcResponse<>();
            Object data = callController(apiRequest.getFunctionCode(), request, response);
            logger.trace("Call controller {}", data);
            if (data != null) {
                if (data instanceof String) {
                    rsp.setRepeatJson(data.toString());
                } else if (data instanceof ResponseContract) {
                    if (null == ((ResponseContract<?>) data).getData()) {
                        // 数据为 null，传个空属性，避免框架空指针异常
                        rsp.setRepeatJson(FastJsonUtils.obj2Json(ResponseContract.success("")));
                    } else {
                        rsp.setRepeatJson(FastJsonUtils.obj2Json(data));
                    }
                } else {
                    rsp.setRepeatJson(FastJsonUtils.obj2Json(data));
                }
            } else {
                rsp.setRepeatJson(FastJsonUtils.obj2Json(ResponseContract.success("")));
            }
        } catch (InvocationTargetException e) {
            LOG.error("InvocationTargetException", e);
            if (e.getTargetException() instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e.getTargetException();
                ResponseContract<String> result = new ResponseContract<>();
                result.setCode(businessException.getCode());
                result.setMsg(businessException.getMessage());
                result.setData(businessException.getMessage());
                rsp.setRepeatJson(FastJsonUtils.obj2Json(result));
            } else {
                rsp.setResult(resultAsList(fail(e.getMessage())));
            }
        } catch (CommonAppException e) {
            LOG.error("CommonAppException", e);
            ResponseContract<String> result = new ResponseContract<>();
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
            result.setData(e.getMessage());
            rsp.setRepeatJson(FastJsonUtils.obj2Json(result));
        } catch (Exception e) {
            LOG.error("Exception", e);
            rsp.setResult(resultAsList(fail(e.getMessage())));
        }
        return rsp;
    }

}
