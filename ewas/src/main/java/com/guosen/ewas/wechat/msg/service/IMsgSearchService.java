package com.guosen.ewas.wechat.msg.service;


import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.msg.domain.bo.MessageBO;
import com.guosen.ewas.wechat.msg.domain.vo.GlobalMsgVO;
import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgPositionVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgGlobalReqVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgSingleReqVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IMsgSearchService {

    /**
     * 通过msgid查询消息
     *
     * @param msgId 消息的id
     * @return 结果
     */
    MessageBO selectByMsgId(String msgId);

    /**
     * 确定会话检索
     *
     * @param search 条件
     * @return 结果
     */
    List<MessageVO> selectSingle(MsgSingleReqVO search);

    /**
     * 全局检索会话检索
     *
     * @param wrapVO 查询
     * @return 结果
     */
    List<GlobalMsgVO> selectGlobal(MsgGlobalReqVO wrapVO);

    PageWrapVO<GlobalMsgVO> selectGlobalPage(PageWrapVO<MsgGlobalReqVO> pageWrap);

    void selectGlobalCount(MsgGlobalReqVO pageWrap);

    /**
     * 查询消息所在位置
     *
     * @param positionVO 条件
     * @return 结果
     */
    List<MessageVO> selectPosition(MsgPositionVO positionVO);

    GlobalMsgVO getByMsgId(String msgId);
}
