package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.MixedWrap;

/**
 * <AUTHOR>
 * @date 2022/1/5 11:32
 * mixed。String类型, 标识混合消息类型
 */
public class MixedDTO extends DataBaseDTO{

    /**
     * mixed
     */
    @JsonProperty("mixed")
    private MixedWrap mixed;

    public MixedWrap getMixed() {
        return mixed;
    }

    public void setMixed(MixedWrap mixed) {
        this.mixed = mixed;
    }

    @Override
    public String toString() {
        return "MixedDTO{" +
                "mixed=" + mixed +
                '}';
    }
}
