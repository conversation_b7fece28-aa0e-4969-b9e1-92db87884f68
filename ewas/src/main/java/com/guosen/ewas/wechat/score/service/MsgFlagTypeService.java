package com.guosen.ewas.wechat.score.service;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.guosen.ewas.wechat.common.constant.DBConsts;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.common.exception.BusinessException;
import com.guosen.ewas.wechat.score.dao.MsgFlagTypeMapper;
import com.guosen.ewas.wechat.score.domain.entity.MsgFlagType;
import com.guosen.ewas.wechat.score.domain.vo.MsgFlagTypeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MsgFlagTypeService {
    private final MsgFlagTypeMapper msgFlagTypeMapper;
    private final MsgFlagService msgFlagService;

    public int deleteByPrimaryKey(Long id) {
        MsgFlagType exists = msgFlagTypeMapper.selectByPrimaryKey(id);
        if (null == exists) {
            return DBConsts.FAIL;
        }
        if (exists.getIsDefault()) {
            throw new BusinessException("默认配置不可删除。");
        }
        if (msgFlagService.existByFlagType(id)) {
            throw new BusinessException("该类型下存在数据，不可删除。");
        }
        return msgFlagTypeMapper.deleteByPrimaryKey(id);
    }

    public Integer insertSelective(MsgFlagType record) {
        if (null != msgFlagTypeMapper.getByName(record.getName())) {
            throw new BusinessException("该类型名称已存在。");
        }
        return msgFlagTypeMapper.insertSelective(record);
    }

    public MsgFlagType selectByPrimaryKey(Long id) {
        return msgFlagTypeMapper.selectByPrimaryKey(id);
    }

    public int enable(Long id, Boolean enable) {
        MsgFlagType upd = new MsgFlagType();
        upd.setId(id);
        upd.setIsEnable(enable);
        return msgFlagTypeMapper.updateByPrimaryKeySelective(upd);
    }

    public Integer updateByPrimaryKeySelective(MsgFlagType record) {
        MsgFlagType exists = msgFlagTypeMapper.selectByPrimaryKey(record.getId());
        if (null == exists) {
            return DBConsts.FAIL;
        }
        if (exists.getIsDefault()) {
            throw new BusinessException("默认配置不可修改。");
        }
        if (!exists.getName().equals(record.getName()) && null != msgFlagTypeMapper.getByName(record.getName())) {
            throw new BusinessException("该类型名称已存在。");
        }
        return msgFlagTypeMapper.updateByPrimaryKeySelective(record);
    }


    public PageInfo<MsgFlagType> list(PageWrapVO<MsgFlagTypeVO> page) {
        MsgFlagType vo = page.getSearch() == null ? new MsgFlagType() :
                BeanUtil.copyProperties(page.getSearch(), MsgFlagType.class);
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).setOrderBy("id desc")
                .doSelectPageInfo(() -> msgFlagTypeMapper.selectByAll(vo));
    }

}
