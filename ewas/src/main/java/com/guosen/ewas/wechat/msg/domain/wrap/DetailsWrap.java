package com.guosen.ewas.wechat.msg.domain.wrap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DetailsWrap {
    /**
     * 表项id。Uint64类型
     */
    @JsonProperty("id")
    private Integer id;
    /**
     * 表项名称。String类型
     */
    @JsonProperty("ques")
    private String ques;
    /**
     * 表项类型，有Text(文本),Number(数字),Date(日期),Time(时间)。String类型
     */
    @JsonProperty("type")
    private String type;

}
