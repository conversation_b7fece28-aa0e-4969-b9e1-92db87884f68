package com.guosen.ewas.wechat.msg.strategy;

import cn.hutool.core.convert.Convert;
import com.guosen.ewas.wechat.common.util.JacksonUtils;
import com.guosen.ewas.wechat.msg.domain.dto.VoteDTO;
import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.vo.msg.VoteVO;
import com.guosen.ewas.wechat.msg.domain.wrap.VoteWrap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 投票
 *
 * <AUTHOR>
 * @date 2021-9-08 13:32
 **/
@Slf4j
@Component("vote")
public class VoteStrategy implements Strategy {
    @Override
    public VoteVO handleContent(String content, MessageVO messageVO) {
        VoteVO voteVO = Convert.convert(VoteVO.class, messageVO);
        VoteDTO voteDTO = JacksonUtils.json2Obj(content, VoteDTO.class);
        voteVO.setVote(voteDTO.getVote());
        return voteVO;
    }

    @Override
    public String getChatContent(String chatData) {
        VoteDTO voteDTO = JacksonUtils.json2Obj(chatData, VoteDTO.class);
        // 投票主题
        return voteDTO.getVote().getVotetitle();
    }

    @Override
    public VoteWrap handleItem(String content, String msgId) {
        return JacksonUtils.json2Obj(content, VoteWrap.class);
    }
}
