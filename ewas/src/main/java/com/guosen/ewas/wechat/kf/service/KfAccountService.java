package com.guosen.ewas.wechat.kf.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.common.service.IFileInfoHeadService;
import com.guosen.ewas.wechat.kf.constant.KfConsts;
import com.guosen.ewas.wechat.kf.dao.KfAccountMapper;
import com.guosen.ewas.wechat.kf.domain.entity.KfAccount;
import com.guosen.ewas.wechat.kf.domain.vo.KfAccountUserVO;
import com.guosen.ewas.wechat.weixin.cp.IMpKfService;
import com.guosen.ewas.wechat.weixin.cp.dto.kf.GetKfAccountReqDTO;
import com.guosen.ewas.wechat.weixin.cp.dto.kf.KfAccountListResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 微信客服账号业务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KfAccountService {
    private final KfAccountMapper kfAccountMapper;
    private final IMpKfService mpKfService;
    private final KfAccountRelUserService kfAccountRelUserService;
    private final IFileInfoHeadService headService;

    public boolean existByOpenKfid(String openKfid) {
        return StrUtil.isNotBlank(openKfid) && kfAccountMapper.getOneByOpenKfid(openKfid) != null;
    }

    /**
     * 同步客服信息
     */
    public void sync() {
        log.debug("开始同步客服信息");
        int offset = 0;
        while (true) {
            GetKfAccountReqDTO req = new GetKfAccountReqDTO();
            req.setOffset(offset);
            req.setLimit(KfConsts.SYNC_LIMIT);
            KfAccountListResDTO kfAccountList = mpKfService.getKfAccountList(req);
            if (kfAccountList == null || !kfAccountList.success() || CollUtil.isEmpty(kfAccountList.getAccountList())) {
                break;
            }
            List<KfAccountListResDTO.AccountListDTO> accountList = kfAccountList.getAccountList();
            if (CollUtil.isNotEmpty(accountList)) {
                LocalDateTime now = LocalDateTime.now();
                for (KfAccountListResDTO.AccountListDTO accountListDTO : accountList) {
                    // 开始新增
                    KfAccount account = kfAccountMapper.getOneByOpenKfid(accountListDTO.getOpenKfid());
                    if (account == null) {
                        account = new KfAccount();
                        account.setCreateDatetime(now);
                        account.setIsDelete(false);
                    }
                    account.setKfAvatar(accountListDTO.getAvatar());
                    account.setOpenKfid(accountListDTO.getOpenKfid());
                    account.setKfName(accountListDTO.getName());
                    account.setModifyDatetime(now);
                    save(account);
                    // 同步客服人员
                    kfAccountRelUserService.syncByKfAccount(account);
                }
            }
            if (accountList.size() < KfConsts.SYNC_LIMIT) {
                break;
            }
            offset += KfConsts.SYNC_LIMIT;
        }
        log.debug("同步客服信息完成");
    }

    public void save(KfAccount kfAccount) {
        if (StrUtil.isNotBlank(kfAccount.getKfAvatar())) {
            kfAccount.setKfAvatar(headService.uploadAvatar(kfAccount.getKfAvatar(), kfAccount.getOpenKfid()));
        }
        kfAccountMapper.insertOrUpdateSelective(kfAccount);
    }

    public PageInfo<KfAccountUserVO> page(PageWrapVO<KfAccountUserVO> page) {
        KfAccountUserVO search = null == page.getSearch() ? new KfAccountUserVO()
                : BeanUtil.copyProperties(page.getSearch(), KfAccountUserVO.class);
        return PageMethod.startPage(page.getPageNum(), page.getPageSize()).setOrderBy("modify_datetime desc")
                .doSelectPageInfo(() -> selectByAll(search));
    }

    public List<KfAccountUserVO> selectByAll(KfAccountUserVO kfAccount) {
        return kfAccountMapper.selectByAll(kfAccount == null ? new KfAccountUserVO() : kfAccount);
    }

}
