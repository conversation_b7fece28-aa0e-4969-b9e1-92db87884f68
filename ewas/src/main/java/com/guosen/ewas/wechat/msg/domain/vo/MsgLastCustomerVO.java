package com.guosen.ewas.wechat.msg.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.guosen.ewas.wechat.common.domain.CustomerInfo;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * 员工检索-客户列表
 */
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MsgLastCustomerVO extends CustomerInfo {
    /**
     * 最后聊天时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime msgDatetime;

    /**
     * 最后聊天内容
     */
    private String msgContent;

}
