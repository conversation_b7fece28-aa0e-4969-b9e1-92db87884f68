package com.guosen.ewas.wechat.score.domain.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
    * 服务评分统计,按审查者维度
    */
@Data
public class ServiceScoreStatsCheck implements Serializable {
    private Long id;

    /**
    * 审查人员id
    */
    private Long checkId;

    /**
    * 审查月份
    */
    private LocalDate checkDate;

    /**
    * 审查总数
    */
    private Long checkNum;

    /**
    * 扣分备注
    */
    private String remark;

    /**
    * 修改时间
    */
    private LocalDateTime update;

    private static final long serialVersionUID = 1L;
}