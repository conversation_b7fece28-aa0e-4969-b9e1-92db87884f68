package com.guosen.ewas.wechat.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2021/9/17 15:55
 */
@Configuration
public class ScheduleConfig {
    private static final Logger log = LoggerFactory.getLogger(ScheduleConfig.class);

    /** 最大线程数 */
    private static final int MAX_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "scheduler-Thread-";

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(MAX_POOL_SIZE);
        taskScheduler.setThreadNamePrefix(THREAD_NAME_PREFIX);
        taskScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
//        taskScheduler.setAwaitTerminationSeconds(60);
        taskScheduler.initialize();
        log.info("定时任务线程池 ThreadPoolTaskScheduler 初始化成功，最大线程数：{}", MAX_POOL_SIZE);
        return taskScheduler;
    }

}
