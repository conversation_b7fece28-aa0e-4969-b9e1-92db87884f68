package com.guosen.ewas.wechat.weixin.cp.dto.rontend;

/**
 * <AUTHOR>
 */
public class JsSignDTO {
    /**
     * 必填，企业微信的corpID
     */
    private String appId;
    /**
     * 必填，生成签名的时间戳
     */
    private Long timestamp;
    /**
     * 必填，生成签名的随机串
     */
    private String nonceStr;
    /**
     * 必填，签名，见 附录-JS-SDK使用权限签名算法
     */
    private String signature;
    /**
     * 应用的agentid
     */
    private String agentid;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getAgentid() {
        return agentid;
    }

    public void setAgentid(String agentid) {
        this.agentid = agentid;
    }

    @Override
    public String toString() {
        return "JsSignDTO{" +
                "appId='" + appId + '\'' +
                ", timestamp=" + timestamp +
                ", nonceStr='" + nonceStr + '\'' +
                ", signature='" + signature + '\'' +
                ", agentid='" + agentid + '\'' +
                '}';
    }
}
