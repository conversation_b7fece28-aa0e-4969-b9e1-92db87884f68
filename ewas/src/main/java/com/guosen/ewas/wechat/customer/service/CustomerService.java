package com.guosen.ewas.wechat.customer.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.domain.CustomerInfo;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.common.service.IFileInfoHeadService;
import com.guosen.ewas.wechat.common.util.JacksonUtils;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import com.guosen.ewas.wechat.common.util.UserContext;
import com.guosen.ewas.wechat.customer.dao.CustomerMapper;
import com.guosen.ewas.wechat.customer.domain.entity.Customer;
import com.guosen.ewas.wechat.customer.domain.vo.CustomerByScopeReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-06 12:55:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {
    /**
     * 客户信息缓存前缀，根据externalUserId
     */
    private static final String KEY_PRE = "customer:external:";
    /**
     * 缓存externalUserId不存在的客户信息集合
     */
    private static final String KEY_EMPTY_SET = "customer:empty";

    private final CustomerMapper customerMapper;
    private final IFileInfoHeadService fileInfoHeadService;

    /**
     * 通过externalUserid查询客户.
     * 获取聊天消息，会频繁调用此查询方法，因此做缓存。
     *
     * @param externalUserid 客户的userid
     * @return 结果
     */
    public Customer getByExternalUserid(String externalUserid) {
        log.debug("Get by externalUserid:{}", externalUserid);
        String key = KEY_PRE + externalUserid;
        String value = RedisUtils.getStr(key);
        if (StrUtil.isNotBlank(value)) {
            // 之前代码的bug, 缓存的对象没有 externalUserid 属性值.
            Customer customer = JacksonUtils.json2Obj(value, Customer.class);
            if (null != customer && StrUtil.isNotBlank(customer.getExternalUserid())) {
                return customer;
            }
        }
        if (RedisUtils.containsInSet(KEY_EMPTY_SET, externalUserid)) {
            return null;
        }
        Customer customer = customerMapper.selectByExternalUserid(externalUserid);
        if (null != customer) {
            RedisUtils.addStr(key, JacksonUtils.obj2Json(customer), randomTimeout());
        } else {
            // 空集合缓存，防止缓存击穿
            // 此处的过期时间，是针对整个Set的过期时间
            RedisUtils.addInSet(KEY_EMPTY_SET, randomTimeout(), externalUserid);
        }
        return customer;
    }

    /**
     * 通过id查询客户
     *
     * @param id 客户的id
     * @return 结果
     */
    public Customer selectOneById(Long id) {
        log.debug("select one by id:{}", id);
        return customerMapper.selectById(id);
    }

    public int update(Customer customer) {
        if (null != customer && null != customer.getId() &&
                BusinessConsts.DB_SUCCESS.equals(customerMapper.update(customer))) {
            log.debug("update customer by:{}", customer);
            clearCache(selectOneById(customer.getId()).getExternalUserid());
            return BusinessConsts.DB_SUCCESS;
        }
        return BusinessConsts.DB_FAIL;
    }

    /**
     * 通过客户id和员工id查询客户详情
     *
     * @param customerId 客户的id
     * @param userId     员工
     * @return 结果
     */
    public CustomerInfo selectInfoByCustomerIdAndUserId(Long customerId, Long userId) {
        log.debug("select info by customer id:{},user id:{}", customerId, userId);
        if (ObjectUtil.isEmpty(customerId) || ObjectUtil.isEmpty(userId)) {
            return new CustomerInfo();
        }
        return customerMapper.selectInfoByCustomerIdAndUserId(customerId, userId);
    }

    /**
     * 通过客户的external_user和员工的userid查询客户详情
     *
     * @param externalUserid 客户的external_user
     * @param userid         员工的userid
     * @return 结果
     */
    public CustomerInfo selectInfoByExternalUseridAndUserid(String externalUserid, String userid) {
        log.debug("select info by external_id:{},userid:{}", externalUserid, userid);
        if (StrUtil.isEmpty(externalUserid) || StrUtil.isEmpty(userid)) {
            return new CustomerInfo();
        }
        return customerMapper.selectInfoByExternalUseridAndUserid(externalUserid, userid);
    }

    public PageWrapVO<CustomerInfo> selectAllPage(PageWrapVO<CustomerByScopeReqVO> wrapVO) {
        PageWrapVO<CustomerInfo> resultWrap = new PageWrapVO<>();
//        Set<Long> checkUserIds = userService.getAndCheckUserIds(Collections.emptyList(), Collections.emptyList());
//        wrapVO.getSearch().setUserIds(new ArrayList<>(checkUserIds));
        wrapVO.getSearch().setUserIds(UserContext.userScope());
        PageInfo<CustomerInfo> pageInfo = PageHelper.startPage(wrapVO.getPageNum(), wrapVO.getPageSize(), false)
                .doSelectPageInfo(() -> selectByVo(wrapVO.getSearch()));
        resultWrap.setList(pageInfo.getList());
        resultWrap.setPageNum(wrapVO.getPageNum());
        resultWrap.setPageSize(wrapVO.getPageSize());
        return resultWrap;
    }

    public List<CustomerInfo> selectByVo(CustomerByScopeReqVO reqVO) {
        return customerMapper.selectByVo(reqVO);
    }

    List<Customer> selectByAll(Customer customer) {
        return customerMapper.selectByAll(customer);
    }

    /**
     * 上传客户头像到服务器
     *
     * @param customer 客户信息
     */
    public String uploadAvatar(Customer customer) {
        return uploadAvatar(customer.getExternalUserid(), customer.getAvatar());
    }

    public String uploadAvatar(String externalUserId, String avatar) {
        if (StrUtil.isNotBlank(avatar)) {
            return fileInfoHeadService.uploadAvatar(avatar, externalUserId);
        } else {
            log.error("file upload avatar is null");
            return avatar;
        }
    }

    /**
     * 新增客户信息
     *
     * @param customer 客户信息
     * @return int
     */
    public int insertOrUpdateSelective(Customer customer) {
        log.info("Customer insertOrUpdate :{}", customer);
        if (null != customer) {
            customerMapper.insertOrUpdateSelective(customer);
            clearCache(customer.getExternalUserid());
            return BusinessConsts.DB_SUCCESS;
        }
        return BusinessConsts.DB_FAIL;
    }

    /**
     * 通过客户的external_userid集合查询客户信息
     *
     * @return 结果
     */
    public List<CustomerInfo> selectByExternalUserids(List<String> externalUserids) {
        if (CollUtil.isEmpty(externalUserids)) {
            return Collections.emptyList();
        }
        return customerMapper.selectByExternalUserids(externalUserids);
    }

    /**
     * 通过id集合查询客户
     *
     * @param ids id集合
     * @return 结果
     */
    public List<CustomerInfo> selectByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return customerMapper.selectByIds(ids);
    }

    /**
     * 新增和修改的时候，需要清除缓存
     *
     * @param externalUserid 客户的userid
     */
    private void clearCache(String externalUserid) {
        RedisUtils.delete(KEY_PRE + externalUserid);
        RedisUtils.removeInSet(KEY_EMPTY_SET, externalUserid);
    }

    /**
     * 防止缓存雪崩，采用随机时间：1~30天
     *
     * @return 超时时间：1~30天，单位秒
     */
    private Long randomTimeout() {
        return 60 * 60 * 24 * RandomUtil.randomLong(1, 30);
    }


}
