package com.guosen.ewas.wechat.msg.enums;

/**
 * 会话类型
 *
 * <AUTHOR>
 */
public enum ChatType {
    /**
     * 单聊
     */
    SINGLE(1, "single"),
    /**
     * 群聊
     **/
    ROOM(2, "room");

    private final Integer code;
    private final String type;

    ChatType(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
