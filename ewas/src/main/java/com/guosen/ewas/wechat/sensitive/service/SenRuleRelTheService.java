package com.guosen.ewas.wechat.sensitive.service;


import cn.hutool.core.collection.CollUtil;
import com.guosen.ewas.wechat.sensitive.dao.SensitiveRuleRelThesaurusMapper;
import com.guosen.ewas.wechat.sensitive.domain.bo.SenWordBO;
import com.guosen.ewas.wechat.sensitive.domain.po.SensitiveRuleRelThesaurus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 敏感词与规则关联关系服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SenRuleRelTheService {
    private final SensitiveRuleRelThesaurusMapper sensitiveRuleRelThesaurusMapper;

    public List<SenWordBO> selectByRuleId(Long ruleId) {
        return null != ruleId ? sensitiveRuleRelThesaurusMapper.selectByRuleId(ruleId) : Collections.emptyList();
    }

    public int deleteBySensitiveRuleId(Long ruleId) {
        return ruleId != null ? sensitiveRuleRelThesaurusMapper.deleteBySensitiveRuleId(ruleId) : 0;
    }
    public int insertBatch(List<SensitiveRuleRelThesaurus> ruleRelThesaurus) {
        return CollUtil.isNotEmpty(ruleRelThesaurus) ? sensitiveRuleRelThesaurusMapper.insertBatch(ruleRelThesaurus) : 0;
    }
    public Integer deleteByThesaurusIds(List<Long> idList) {
        return CollUtil.isNotEmpty(idList) ? sensitiveRuleRelThesaurusMapper.deleteByThesaurusIds(idList) : 0;
    }

}
