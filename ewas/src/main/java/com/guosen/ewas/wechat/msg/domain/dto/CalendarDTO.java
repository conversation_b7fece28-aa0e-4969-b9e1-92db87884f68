package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.CalendarWrap;

/**
 * calendar。String类型, 标识日程消息类型
 */
public class CalendarDTO extends DataBaseDTO {

    /**
     * calendar
     */
    @JsonProperty("calendar")
    private CalendarWrap calendar;

    public CalendarWrap getCalendar() {
        return calendar;
    }

    public void setCalendar(CalendarWrap calendar) {
        this.calendar = calendar;
    }

    @Override
    public String toString() {
        return "CalendarDTO{" +
                "calendar=" + calendar +
                '}';
    }
}
