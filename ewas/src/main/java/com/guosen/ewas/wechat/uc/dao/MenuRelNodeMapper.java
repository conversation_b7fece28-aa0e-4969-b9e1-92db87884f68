package com.guosen.ewas.wechat.uc.dao;

import com.guosen.ewas.wechat.uc.domain.po.Menu;
import com.guosen.ewas.wechat.uc.domain.po.MenuRelNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface MenuRelNodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MenuRelNode record);

    int insertSelective(MenuRelNode record);

    MenuRelNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MenuRelNode record);

    int updateByPrimaryKey(MenuRelNode record);

    int deleteByNodeId(@Param("nodeId") Long nodeId);

    Set<Long> selectMenuIdByNodeId(@Param("nodeId") Long nodeId);

    MenuRelNode selectByMenuIdAndNodeId(@Param("menuId") Long menuId, @Param("nodeId") Long nodeId);

    List<Menu> selectByNodeIds(@Param("nodeIds") List<Long> nodeIds);
}