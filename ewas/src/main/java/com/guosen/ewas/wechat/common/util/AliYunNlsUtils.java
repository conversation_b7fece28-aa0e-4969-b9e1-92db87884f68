package com.guosen.ewas.wechat.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.guosen.ewas.wechat.common.config.AliYunNlsConfig;
import com.guosen.ewas.wechat.common.domain.dto.AliYunNlsReqDto;
import com.guosen.ewas.wechat.msg.domain.entity.MsgVoiceTrans;
import com.guosen.ewas.wechat.msg.enums.MsgVoiceTransCode;
import com.guosen.ewas.wechat.msg.service.IMsgVoiceTransService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 阿里云录音转写
 *
 * <AUTHOR>
 */
@Component
public class AliYunNlsUtils {
    private static final Logger log = LoggerFactory.getLogger(AliYunNlsUtils.class);
    private static AliYunNlsConfig aliYunNlsConfig;
    //    private static final String URL = "http://112.65.144.19:8088";

    public static void transfer(String fileLink, String msgid) {
        log.debug("nls trans file link:{}", fileLink);
        if (StrUtil.isEmpty(fileLink)) {
            log.error("file link is empty");
            return;
        }
        AliYunNlsReqDto dto = new AliYunNlsReqDto();
        dto.setAppkey(getAppKey());
        dto.setToken(getToken());
        dto.setFile_link(fileLink);
        //是否存在回调地址
        if (StrUtil.isNotEmpty(getCallBackUrl())) {
            dto.setEnable_callback(true);
            dto.setCallback_url(getCallBackUrl());
        } else {
            dto.setEnable_callback(false);
        }
        log.debug("nls trans post dto:{}", dto);
        String postNls = postNls(dto);
        if (StrUtil.isEmpty(postNls)) {
            log.error("post nls res is empty");
            return;
        }
        log.debug("nls trans post res:{}", postNls);
        JSONObject jsonResult = JSONObject.parseObject(postNls);
        JSONObject jsonHeard = jsonResult.getJSONObject("header");
        MsgVoiceTrans msgVoiceTrans = new MsgVoiceTrans();
        msgVoiceTrans.setMsgid(msgid);
        msgVoiceTrans.setFileLink(fileLink);
        msgVoiceTrans.setStatus(jsonHeard.getInteger("status"));
        msgVoiceTrans.setStatusMessage(jsonHeard.getString("status_message"));
        msgVoiceTrans.setTaskId(jsonHeard.getString("task_id"));
        msgVoiceTrans.setCreateDatetime(LocalDateTime.now());
        //传输成功
        log.info("nls trans msg vice trans:{}", msgVoiceTrans);
        if (MsgVoiceTransCode.SUCCESS.getCode().equals(msgVoiceTrans.getStatus())) {
            msgVoiceTrans.setType(0);
        } else {
            //传输失败
            msgVoiceTrans.setType(-2);
            log.error("aliyun nls res dto error:{}", postNls);
        }
        IMsgVoiceTransService msgVoiceTransService = SpringUtils.getBean(IMsgVoiceTransService.class);
        msgVoiceTransService.insert(msgVoiceTrans);
    }

    public static String postNls(AliYunNlsReqDto reqDto) {
        String url = getUrl() + "/stream/v1/filetrans";
        return postNls(url, reqDto);
    }

    public static String getNls(String taskId) {
        if (StrUtil.isEmpty(taskId)) {
            return null;
        }
        String url = getUrl() + "/stream/v1/filetrans?task_id=" + taskId;
        return getNls(url, null);
    }

    private static String postNls(String url, Object params) {
        log.debug("Post nls url:{}, {}", url, params);
        HttpRequest httpRequest = HttpRequest.post(url).body(JacksonUtils.obj2Json(params));
        return httpRequest.execute().body();
    }

    private static String getNls(String url, Object params) {
        HttpRequest httpRequest = HttpRequest.get(url).form(JacksonUtils.obj2Map(params));
        return httpRequest.execute().body();
    }

    /**
     * 语音转写的appKey，默认为default
     */
    private static String getAppKey() {
        return StrUtil.isEmpty(aliYunNlsConfig.getAppKey()) ? "default" : aliYunNlsConfig.getAppKey();
    }

    /**
     * 语音转写的请求地址（ip+端口）
     */
    private static String getUrl() {
        return aliYunNlsConfig.getUrl();
    }

    /**
     * 语音转写的回调地址
     */
    private static String getCallBackUrl() {
        return aliYunNlsConfig.getCallBackUrl();
    }

    /**
     * 语音转写的token，默认为default
     */
    public static String getToken() {
        return StrUtil.isEmpty(aliYunNlsConfig.getToken()) ? "default" : aliYunNlsConfig.getToken();
    }

    @Autowired
    public void setAliYunNlsConfig(AliYunNlsConfig aliYunNlsConfig) {
        AliYunNlsUtils.aliYunNlsConfig = aliYunNlsConfig;
    }

}
