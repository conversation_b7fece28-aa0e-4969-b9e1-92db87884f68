package com.guosen.ewas.wechat.msg.strategy;

import cn.hutool.core.convert.Convert;
import com.guosen.ewas.wechat.common.util.JacksonUtils;
import com.guosen.ewas.wechat.msg.domain.dto.SphfeedDTO;
import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.vo.msg.SphfeedVO;
import com.guosen.ewas.wechat.msg.domain.wrap.SphfeedWrap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 视频号
 *
 * <AUTHOR>
 * @date 2021-9-08 13:32
 **/
@Slf4j
@Component("sphfeed")
public class SphfeedStrategy implements Strategy {
    @Override
    public SphfeedVO handleContent(String content, MessageVO messageVO) {
        SphfeedVO sphfeedVO = Convert.convert(SphfeedVO.class, messageVO);
        SphfeedDTO sphfeedDTO = JacksonUtils.json2Obj(content, SphfeedDTO.class);
        sphfeedVO.setSphfeed(sphfeedDTO.getSphfeed());
        return sphfeedVO;
    }

    @Override
    public String getChatContent(String chatData) {
        SphfeedDTO sphfeedDTO = JacksonUtils.json2Obj(chatData, SphfeedDTO.class);
        // 视频号账号名称
        return sphfeedDTO.getSphfeed() == null ? "" : sphfeedDTO.getSphfeed().getSphName();
    }

    @Override
    public SphfeedWrap handleItem(String content, String msgId) {
        return JacksonUtils.json2Obj(content, SphfeedWrap.class);
    }
}
