package com.guosen.ewas.wechat.msg.domain.vo.msg;

import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.wrap.RedpacketWrap;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * redpacket。String类型
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RedpacketVO extends MessageVO {

    private RedpacketWrap redpacket;

}
