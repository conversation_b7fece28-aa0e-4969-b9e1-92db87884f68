package com.guosen.ewas.wechat.score.dao;
import java.util.Date;

import com.guosen.ewas.wechat.score.domain.entity.MsgFlagType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MsgFlagTypeMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(MsgFlagType record);

    MsgFlagType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MsgFlagType record);

    int batchInsert(@Param("list") List<MsgFlagType> list);

    MsgFlagType getByName(@Param("name") String name);

    List<MsgFlagType> selectByAll(MsgFlagType msgFlagType);
}