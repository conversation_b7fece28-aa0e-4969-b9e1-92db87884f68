package com.guosen.ewas.wechat.job;

import cn.hutool.core.util.StrUtil;
import com.guosen.ewas.wechat.common.constant.DateConsts;
import com.guosen.ewas.wechat.common.constant.RedisKeyConsts;
import com.guosen.ewas.wechat.common.redis.RedisDistributedLockHelper;
import com.guosen.ewas.wechat.common.util.DateUtils;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import com.guosen.ewas.wechat.reply.service.impl.ReplyTimeoutHandler;
import com.guosen.zebra.distributed.lock.DistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 超时恢复检测定时
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-09-11 17:19
 */
@Component
public class ReplyTimeoutJob {

    private static final Logger log = LoggerFactory.getLogger(ReplyTimeoutJob.class);

    @Resource
    ReplyTimeoutHandler replyTimeoutHandler;

    private static String COMPLETE = "1";

    /**
     * 每分钟执行
     */
    @Scheduled(cron = "0 * * * * ?")
    public void schedule() {
        log.debug("Reply timeout job handler");
        // 集群要考虑同步锁问题：这里采用针对单条数据加锁
        // 定时器不加锁，业务处理单条数据时加锁即可。
        replyTimeoutHandler.replyTimeoutJobHandler();
    }

    /**
     * 每10秒执行一次
     */
    @Scheduled(cron = "0/10 * * * * ?")
    public void msgQueueTask() {
        replyTimeoutHandler.popMsg();
    }

    @Scheduled(cron = "0 10 3 * * ?")
    public void scheduleStats() {
        log.info("Reply timeout stats day start.");
        // 每日执行一次数据统计汇总
        // 集群部署，定时器加锁处理，每日只需要一个执行即可。
        DistributedLock lock = RedisDistributedLockHelper.getLock(RedisKeyConsts.JobLock.REPLY_TIMEOUT_STATS, 10 * 60L);
        if (lock == null || !lock.tryLock()) {
            log.info("Reply timeout stats day get lock(key = {}) fail. skip job. {}",
                    RedisKeyConsts.JobLock.REPLY_TIMEOUT_STATS, lock);
            return;
        }
        try {
            // 集群统计，即时加锁依旧可能出现多台重复统计。
            // 增加业务缓存标识，规避重复统计。
            String key = StrUtil.format(RedisKeyConsts.ReplyTimeout.STATS_FLAG
                    , DateUtils.date2Str(LocalDate.now().minusDays(1), DateConsts.FORMAT_DATE));
            String flag = RedisUtils.getStr(key);
            if (!COMPLETE.equals(flag)) {
                log.debug("Reply timeout stats day exec.");
                replyTimeoutHandler.replyTimeoutStatsJobHandler();
                RedisUtils.addStr(key, COMPLETE, RedisKeyConsts.ReplyTimeout.TIMEOUT);
            } else {
                log.warn("Reply timeout stats day is complete. cache flag ={}", flag);
            }
        } finally {
            log.debug("Reply timeout stats day unlock.");
            lock.unlock();
        }
        log.info("Reply timeout stats day end.");
    }
}
