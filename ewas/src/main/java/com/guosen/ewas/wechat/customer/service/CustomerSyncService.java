package com.guosen.ewas.wechat.customer.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.guosen.ewas.wechat.common.config.ThreadPoolConfig;
import com.guosen.ewas.wechat.common.constant.BusinessConsts;
import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.constant.DateConsts;
import com.guosen.ewas.wechat.common.constant.RedisKeyConsts;
import com.guosen.ewas.wechat.common.redis.RedisDistributedLockHelper;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.common.service.IFileInfoHeadService;
import com.guosen.ewas.wechat.common.util.*;
import com.guosen.ewas.wechat.customer.domain.entity.Customer;
import com.guosen.ewas.wechat.weixin.cp.ICpCustomerService;
import com.guosen.ewas.wechat.weixin.cp.dto.customer.DetailDTO;
import com.guosen.ewas.wechat.weixin.cp.dto.customer.ListDTO;
import com.guosen.zebra.distributed.lock.DistributedLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-06 12:55:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerSyncService {
    private final ICpCustomerService cpCustomerService;
    private final UserCustomerRelationshipService relationService;
    private final ThreadPoolConfig threadPoolConfig;
    private final ConfigKvService configKvService;
    private final GroupSyncService groupSyncService;
    private final CustomerService customerService;
    private final IFileInfoHeadService fileInfoHeadService;

    /**
     * 同步客户
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void syncCustomerByExternalUserid(String externalUserid) {
        log.debug("sync customer by external userid start:{}", externalUserid);
        if (StrUtil.isEmpty(externalUserid)) {
            log.debug("sync customer,external userid is empty");
            return;
        }
        //向企业微信请求客户详情
        DetailDTO detail = cpCustomerService.detail(externalUserid);
        //向企业微信请求出错
        if (detail.fail()) {
            log.warn("sync customer error id：{}, {}", externalUserid, JacksonUtils.obj2Json(detail));
            return;
        }
        //查询是否存在客户关系
        Customer customer = customerService.getByExternalUserid(externalUserid);
        Customer wxCustomer = Convert.convert(Customer.class, detail.getExternalContact());
        String avatar = customerService.uploadAvatar(wxCustomer);
        if (customer != null) {
            Customer combineCustomer = BeanUtils.combine(wxCustomer, customer);
            combineCustomer.setAvatar(avatar);
            log.debug("sync customer select by external userid is not null");
            //更新
            customerService.update(combineCustomer);
            //同步关系
            relationService.syncRelationship(detail.getFollowUser(), combineCustomer);
        } else {
            wxCustomer.setAvatar(avatar);
            wxCustomer.setCreateDatetime(LocalDateTime.now());
            if (StrUtil.isBlank(wxCustomer.getName())) {
                wxCustomer.setName("未命名");
            }
            //新增客户
            if (BusinessConsts.DB_SUCCESS.equals(customerService.insertOrUpdateSelective(wxCustomer))) {
                //同步关系
                relationService.syncRelationship(detail.getFollowUser(), wxCustomer);
            }
        }
    }

    /**
     * 通过员工的userid请求到关联的所有客户，
     * 然后客户ID加入缓存队列。
     *
     * @param userid 员工的userid
     */
    private void getExternalUseridByUseridFromWx(String userid) {
        if (StrUtil.isBlank(userid)) {
            return;
        }
        log.debug("Get customer externalUserid by userid:{} ", userid);
        ListDTO list = cpCustomerService.list(userid);
        if (null == list || !ObjectUtil.equals(list.getErrcode(), 0)) {
            //获取内容出错
            log.error("cp customer list error:{}", list);
            return;
        }
        log.debug("list get external userid list size:{}", list.getExternalUserid());
        if (CollUtil.isNotEmpty(list.getExternalUserid())) {
            list.getExternalUserid().forEach(e -> {
                //加入所有的客户的external_userid
                RedisUtils.addInSet(RedisKeyConsts.SYNC_CUSTOMER_EXTERNALUSERID, RedisKeyConsts.TimeOut.THIRTY_DAY, e);
            });
        }
    }


    /**
     * 同步客户(企业微信外部联系人。微信 -> 当前系统)。 <br/>
     * <p>
     * 调用场景：系统初始化，夜里定时器同步客户
     * </p>
     * <p>
     * 主要过程：<br/>
     * 1、获取待同步客户的员工(user)<br/>
     * 2、获取员工关联的客户（外部联系人）<br/>
     * 3、获取客户(外部联系人)详情<br/>
     * </p>
     * <p>
     * 客户同步考虑微信并发限制问题,系统做每分钟默认调用2000次限制。<br/>
     * 微信限制：每企业调用单个cgi/api不可超过1万次/分，15万次/小时<br/>
     * 说明见：https://work.weixin.qq.com/api/doc/90000/90139/90312
     * </p>
     */
    public void sync() {
        // 项目集群部署，员工id和客户id都存入redis集合，让多台机器同时处理。
        // 线程自循环，当所有员工信息都被同步，则结束循环
        log.debug("sync external userid by userid start");
        while (true) {
            // 1、获取待同步客户的员工(user)
            Object cache = RedisUtils.popSet(RedisKeyConsts.SYNC_NEED_USERID);
            log.debug("Sync user(id={}) customer", cache);
            if (null == cache) {
                // 说明队列没数据了，直接结束循环
                log.debug("Cache external userid key is empty. key={}", "");
                break;
            }

            // 2、获取员工关联的客户（外部联系人）<br/>
            getExternalUseridByUseridFromWx(cache.toString());
            // 3、获取客户(外部联系人)详情<br/>
            syncDetailByExternal();
            //同步客户群
            groupSyncService.syncChatidByOwnerUserid(cache.toString());
            groupSyncService.syncDetail();
        }
        log.debug("sync external userid by userid end");
    }

    /**
     * 同步客户<br/>
     * 1、从缓存集合中获取要同步的数据：RedisKeyConsts.SYNC_CUSTOMER_EXTERNALUSERID<br/>
     * 2、启用线程池同步客户信息<br/>
     * 3、同步客户数据量比较大，会造成触发微信接口限制，因此需要做单接口限制判断。<br/>
     * 获取缓存集合中要同步的数据，直到同步完成为止。<br/>
     * 缓存客户集合存储key：RedisKeyConsts.SYNC_CUSTOMER_EXTERNALUSERID<br/>
     */
    public void syncDetailByExternal() {
        // 线程自循环，当所有客户信息都被同步，则结束循环
        log.debug("sync detail by external start");
        while (true) {
            if (checkApiLimit()) {
                Object cache = RedisUtils.popSet(RedisKeyConsts.SYNC_CUSTOMER_EXTERNALUSERID);
                if (null == cache) {
                    // 说明队列没数据了，直接结束循环
                    log.debug("Cache get detail key is empty. key={}", cache);
                    break;
                }
                // 同步客户，在项目启动和夜里定时器同步的时候
                // 因此可加入线程池，提升处理速度
                threadPoolConfig.getAsyncExecutor().execute(() -> initCustomer(String.valueOf(cache)));
            }
        }
        log.debug("sync detail by external end");
    }

    @Transactional(rollbackFor = Exception.class)
    public void initCustomer(String externalUserid) {
        if (StrUtil.isBlank(externalUserid)) {
            return;
        }
        DistributedLock lock = RedisDistributedLockHelper.getLock(
                RedisKeyConsts.LOCK_SYNC_CUSTOMER + externalUserid, 3600);
        if (lock == null || !lock.tryLock()) {
            log.error("获取客户external_user init锁失败");
            return;
        }
        try {
            Customer customer = customerService.getByExternalUserid(externalUserid);
            if (customer != null) {
                return;
            }
            log.debug("sync customer by external userid start:{}", externalUserid);
            if (StrUtil.isEmpty(externalUserid)) {
                log.debug("sync customer,external userid is empty");
                return;
            }
            //向企业微信请求客户详情
            DetailDTO detail = cpCustomerService.detail(externalUserid);
            //向企业微信请求出错
            if (!Integer.valueOf(0).equals(detail.getErrcode())) {
                log.warn("获取客户详情出错：{}", JacksonUtils.obj2Json(detail));
                return;
            }
            //查询是否存在客户关系
            Customer wxCustomer = Convert.convert(Customer.class, detail.getExternalContact());
            wxCustomer.setCreateDatetime(LocalDateTime.now());
            customerService.uploadAvatar(wxCustomer);
            //更新头像
            if (StrUtil.isNotEmpty(wxCustomer.getAvatar())) {
                String uploadAvatar = fileInfoHeadService.uploadAvatar(wxCustomer.getAvatar(), wxCustomer.getExternalUserid());
                if (StrUtil.isNotEmpty(uploadAvatar)) {
                    wxCustomer.setAvatar(uploadAvatar);
                }
            }
            //新增客户
            if (BusinessConsts.DB_SUCCESS.equals(customerService.insertOrUpdateSelective(wxCustomer))) {
                log.debug("get follow user size:{}", detail.getFollowUser().size());
                //同步关系
                relationService.initRelation(detail.getFollowUser(), wxCustomer);
            }
        } catch (Exception e) {
            log.error("获取客户external_user init 任务出错", e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检测微信API接口限制：<br/>
     * 限制次数：默认每分钟2000次
     * 微信限制：每企业调用单个cgi/api不可超过1万次/分，15万次/小时<br/>
     * 说明见：https://work.weixin.qq.com/api/doc/90000/90139/90312<br/>
     * <p>
     * true: 未限制<br/>
     * false: 接口超限，线程休眠10秒值<br/>
     *
     * @return boolean
     */
    public boolean checkApiLimit() {
        String time = RedisKeyConsts.API_LIMIT + DateUtils.time2Str(LocalDateTime.now(), DateConsts.FORMAT_YYYYMMDDHHMM);
        Integer increment = RedisUtils.increment(time, 1, 300L);
        if (increment > getLimit()) {
            try {
                // 单分钟接口超限，线程休眠10秒
                log.debug("Sync customer get api limit sleep 10s");
                Thread.sleep(10000L);
            } catch (InterruptedException e) {
                log.error("Sync customer get api limit sleep exception ", e);
            }
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 通过客户的 external_userid , 先虚拟客户然后异步执行同步方法
     */
    public void buildVirtualCustomer(String externalUserid) {
        if (StrUtil.isBlank(externalUserid)) {
            return;
        }
        Customer exist = customerService.getByExternalUserid(externalUserid);
        if (null != exist) {
            return;
        }
        Customer customer = new Customer();
        customer.setExternalUserid(externalUserid);
        customer.setName(StrUtil.format("未加企微好友（{}）", externalUserid));
        customer.setGender(0);
        customer.setType(1);
        customer.setAvatar("");
        customer.setModifyDatetime(LocalDateTime.now());
        customer.setCreateDatetime(LocalDateTime.now());
        customerService.insertOrUpdateSelective(customer);
        syncCustomerByExternalUserid(externalUserid);
    }

    public void syncVirtualCustomer() {
        Customer search = new Customer();
        search.setName("未加企微好友");
        // 分页查询
        int pageNum = 1;
        while (true) {
            PageInfo<Customer> page = PageHelper.startPage(pageNum, 1000)
                    .setOrderBy("id desc")
                    .doSelectPageInfo(() -> customerService.selectByAll(search));
            if (null == page || CollUtil.isEmpty(page.getList())) {
                break;
            }
            pageNum++;
            page.getList().forEach(c -> {
                if (c.getName().contains(c.getExternalUserid())) {
                    syncCustomerByExternalUserid(c.getExternalUserid());
                }
            });
        }
    }

    private Integer getLimit() {
        String num = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SYSTEM, ConfigConsts.Key.API_LIMIT_MINUTE);
        return StrUtil.isBlank(num) ? 2000 : Integer.parseInt(num);
    }

}
