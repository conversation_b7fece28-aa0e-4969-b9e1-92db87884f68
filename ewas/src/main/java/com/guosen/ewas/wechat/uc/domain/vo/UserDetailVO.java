package com.guosen.ewas.wechat.uc.domain.vo;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class UserDetailVO {
    /**
     *
     */
    private Long id;

    /**
     * 成员userid
     */
    private String userid;

    /**
     * 成员名称
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 职务信息
     */
    private String position;

    /**
     * 性别。0表示未定义，1表示男性，2表示女性
     */
    private String gender;

    private String avatar;

    /**
     * 所在部门
     */
    private Collection<Dept> depts;

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public static class Dept {
        private Long id;
        private String name;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public Collection<Dept> getDepts() {
        return depts;
    }

    public void setDepts(Collection<Dept> depts) {
        this.depts = depts;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    @Override
    public String toString() {
        return "UserDetailVO{" +
                "id=" + id +
                ", userid='" + userid + '\'' +
                ", name='" + name + '\'' +
                ", mobile='" + mobile + '\'' +
                ", position='" + position + '\'' +
                ", gender='" + gender + '\'' +
                ", avatar='" + avatar + '\'' +
                ", depts=" + depts +
                '}';
    }
}
