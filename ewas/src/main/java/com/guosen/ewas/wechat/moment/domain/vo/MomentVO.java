package com.guosen.ewas.wechat.moment.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 朋友圈记录
 */
@Data
public class MomentVO implements Serializable {
    /**
     * 朋友圈ID
     */
    private String momentId;
    /**
     * 朋友圈创建者userid，企业发表内容到客户的朋友圈接口创建的朋友圈不再返回该字段
     */
    private String creator;
    /**
     * 创建时间，秒值时间戳
     */
    private Long createTime;
    /**
     * 朋友圈创建来源。0：企业 1：个人
     */
    private Integer createType;
    /**
     * 可见范围类型。0：部分可见 1：公开
     */
    private Integer visibleType;
    /**
     * 文本消息结构
     */
    private String textContent;
    /**
     * 图片链接地址
     */
    private List<String> imageUrl;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 视频封面地址
     */
    private String videoThumbUrl;
    /**
     * 网页链接标题
     */
    private String linkTitle;
    /**
     * 网页链接url
     */
    private String linkUrl;
    /**
     * 地理位置纬度
     */
    private String locationLatitude;
    /**
     * 地理位置经度
     */
    private String locationLongitude;
    /**
     * 地理位置名称
     */
    private String locationName;
    /**
     * 数据创建时间
     */
    private LocalDateTime createDatetime;
    /**
     * 获取客户朋友圈发表时选择的可见范围数
     */
    private Integer visibleCount = 0;
    /**
     * 获取客户朋友圈发表后的可见客户数
     */
    private Integer customerVisibleCount = 0;
    /**
     * 喜欢总数
     */
    private Integer likeCount = 0;
    /**
     * 评论总数
     */
    private Integer commentCount = 0;
    /**
     * 企业朋友圈：已发送人数
     */
    private Integer sentCount = 0;
    /**
     * 企业朋友圈：未发送人数
     */
    private Integer unsentCount = 0;
    /**
     * 用户名称（发送者）
     */
    private String userName;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户头像地址
     */
    private String userAvatar;

}