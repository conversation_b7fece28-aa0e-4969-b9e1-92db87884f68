package com.guosen.ewas.wechat.customer.dao;

import com.guosen.ewas.wechat.customer.domain.entity.GroupChatLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户群Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-06 14:07:06
 */
@Mapper
public interface GroupChatLogMapper {

    /**
     * 新增群数据统计
     *
     * @param groupChatLog 群数据统计
     * @return 结果
     */
    int insert(GroupChatLog groupChatLog);

    /**
     * 批量新增群数据统计
     *
     * @param groupChatLogs 群数据统计
     * @return 结果
     */
    int insertBatch(List<GroupChatLog> groupChatLogs);

    /**
     * 查询活跃客户数
     *
     * @param ownerIds  群主id集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @Deprecated
    Integer selectActiveCustomerCountByOwnerIds(@Param("ownerIds") List<Long> ownerIds, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    /**
     * 查询该群主昨日的活跃客户
     *
     * @param ownerId   群主id
     * @param yesterday 昨日时间
     * @return 结果
     */
    @Deprecated
    Integer selectYesterdayActiveCustomerCountByOwnerId(@Param("ownerId") Long ownerId, @Param("yesterday") LocalDate yesterday);
}
