package com.guosen.ewas.wechat.sensitive.constant;

/**
 * 敏感词规则常量
 *
 * <AUTHOR>
 */
public interface SensitiveConts {

    /**
     * 敏感词规则默认名称
     */
    String RULE_NAME_DEFAULT = "未命名";

    /**
     * 敏感词规则，员工之间触发
     */
    int TRIGGERED_BETWEEN_USER_TRUE = 1;

    /**
     * 敏感词规则，员工之间不触发
     */
    int TRIGGERED_BETWEEN_USER_FALSE = 0;

    /**
     * 触发类型：仅员工
     */
    int TRIGGER_TYPE_USER = 1;
    /**
     * 触发类型：仅客户
     */
    int TRIGGER_TYPE_CUSTOMER = 2;
    /**
     * 触发类型：员工和客户
     */
    int TRIGGER_TYPE_USER_AND_CUSTOMER = 3;

    String CACHE_WORD_VERSION = "sensitive:word:version";
    String CACHE_WORD_CHANGE_KEY = "lock:sensitive:word:update";


}
