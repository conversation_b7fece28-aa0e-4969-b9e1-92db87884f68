package com.guosen.ewas.wechat.sensitive.domain.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 敏感词规则
 * <AUTHOR>
 */
@Data
public class SensitiveRule {
    private Long id;

    /**
     * 规则名
     */
    private String ruleName;

    /**
     * 敏感词触发类型 1:仅员工触发,2仅客户触发,3:员工和客户都能触发（默认）
     */
    private Integer sensitiveTriggerType;

    /**
     * 是否员工间能触发 1：是，0：否（默认）
     */
    private Boolean isBetweenUser;

    /**
     * 关联的部门ids
     */
    private String deptIds;

    /**
     * 创建时间
     */
    private LocalDateTime createDatetime;

    /**
     * 更新时间
     */
    private LocalDateTime modifyDatetime;

    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 是否逻辑删除：1--已删除；0--未删除（默认）
     */
    private Boolean isDelete;

    /**
     * 群消息监控配置：0:不开启（默认），1：外部群，2：内部群，3：开启（外部+内部）
     */
    private Integer groupOpen;
}