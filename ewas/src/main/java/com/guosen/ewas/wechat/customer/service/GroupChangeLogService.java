package com.guosen.ewas.wechat.customer.service;

import com.guosen.ewas.wechat.customer.dao.GroupChangeLogMapper;
import com.guosen.ewas.wechat.customer.domain.bo.GroupMemberChangeBO;
import com.guosen.ewas.wechat.customer.domain.entity.GroupChangeLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 群变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-16 16:30:06
 */
@Service
@RequiredArgsConstructor
public class GroupChangeLogService {
    private final GroupChangeLogMapper groupChangeLogMapper;

    public int insert(GroupChangeLog groupChangeLog) {
        return groupChangeLogMapper.insert(groupChangeLog);
    }

    /**
     * 根据群 ID 统计入群/离群人数
     */
    public List<GroupMemberChangeBO> statsGroup(Collection<Long> groupIds, LocalDate statsDate) {
        return groupChangeLogMapper.statsGroup(groupIds, statsDate);
    }

    /**
     * 根据群主 ID 统计其所管理群的 入群/离群人数
     */
    public List<GroupMemberChangeBO> statsGroupOwner(Collection<Long> userIds, LocalDate statsDate) {
        return groupChangeLogMapper.statsGroupOwner(userIds, statsDate);
    }

}
