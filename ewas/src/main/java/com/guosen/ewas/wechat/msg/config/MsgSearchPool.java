package com.guosen.ewas.wechat.msg.config;

import com.guosen.ewas.wechat.common.exception.ExceptionHandlingTaskDecorator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息查询线程池，专门处理消息查询的线程池
 */
public class MsgSearchPool {
    private static final Logger log = LoggerFactory.getLogger(MsgSearchPool.class);

    /**
     * 核心线程数
     */
    public static final int CORE_POOL_SIZE = 5;

    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 10;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "Msg-Search-";

    private static final ThreadPoolTaskExecutor EXECUTOR = new ThreadPoolTaskExecutor();

    static {
        EXECUTOR.setCorePoolSize(CORE_POOL_SIZE);
        EXECUTOR.setMaxPoolSize(MAX_POOL_SIZE);
        // CallerRunsPolicy 如果达到最大值，就使用当前线程执行，不再开辟新线程
        // AbortPolicy 如果达到最大值，就拒绝当前任务并抛出异常
        EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR.setThreadNamePrefix(THREAD_NAME_PREFIX);
        EXECUTOR.setQueueCapacity(0);
        // 这是一种非常简洁的方法，可以让您更方便地为线程池中的所有任务添加异常处理。
        EXECUTOR.setTaskDecorator(new ExceptionHandlingTaskDecorator());
        EXECUTOR.setKeepAliveSeconds(300);
        EXECUTOR.setAllowCoreThreadTimeOut(true);
        EXECUTOR.initialize();
        log.info("会话消息检索-异步线程池 ThreadPoolTaskExecutor 初始化成功，核心线程数：{}，最大线程数：{}", CORE_POOL_SIZE, MAX_POOL_SIZE);
    }



    /**
     * 获取线程池实例
     */
    public static ThreadPoolTaskExecutor getExecutor() {
        return EXECUTOR;
    }

    /**
     * 提交有返回值的任务到线程池
     */
    public static <T> CompletableFuture<T> supplyAsync(java.util.function.Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, EXECUTOR.getThreadPoolExecutor());
    }
}
