package com.guosen.ewas.wechat.kf.enums;

/**
 * 消息来源。3-微信客户发送的消息 4-系统推送的事件消息 5-接待人员在企业微信客户端发送的消息
 *
 * <AUTHOR>
 * @date 2022/1/22 11:49
 */
public enum OriginEnum {
    /**
     * 微信客户发送的消息
     **/
    SEND_BY_CUSTOMER(3, "send_by_customer"),
    /**
     * 系统推送的事件消息
     **/
    SEND_BY_SYS(4, "send_by_sys"),
    /**
     * 接待人员在企业微信客户端发送的消息
     **/
    SEND_BY_USER_FROM_WX_CLIENT(5, "send_by_user_from_wx_client"),
    /**
     * 未知
     **/
    OTHER(0, "未知");

    private final Integer code;
    private final String type;

    OriginEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public static OriginEnum valueOf(Integer code) {
        OriginEnum[] values = values();
        for (OriginEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return OTHER;
    }

    public static OriginEnum typeOf(String type) {
        OriginEnum[] values = values();
        for (OriginEnum value : values) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return OTHER;
    }

    public static OriginEnum codeOf(Integer code) {
        OriginEnum[] values = values();
        for (OriginEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return OTHER;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
