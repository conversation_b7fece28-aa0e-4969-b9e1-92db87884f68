package com.guosen.ewas.wechat.moment.domain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户朋友圈的互动数据
 */
@Data
public class MomentComments implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 朋友圈ID
     */
    private String momentId;

    /**
     * 企业发表成员userid，对应：moment的creator。企业发送需要独立记录每个人的情况，因此这个表需要此数据。
     */
    private String creator;

    /**
     * 互动员工
     */
    private String userid;

    /**
     * 互动客户，客户与员工不会同时出现
     */
    private String externalUserid;

    /**
     * 互动用户类型：0：员工，1：客户，
     */
    private Integer userType;

    /**
     * 互动行为：0:点赞 1: 评论
     */
    private Integer behavior;

    /**
     * 评论内容,预留字段
     */
    private String content;

    /**
     * 互动时间
     */
    private Long createTime;

    /**
     * 创建时间
     */
    private LocalDateTime createDatetime;

    /**
     * 备注信息
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}