package com.guosen.ewas.wechat.weixin.cp.dto.kf.wrap.item;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 文本消息
 *
 * <AUTHOR>
 * @date 2022/1/21 16:56
 */
@Data
public class TextItemWrap {
    /**
     * 文本内容
     */
    @JsonProperty("content")
    private String content;
    /**
     * 客户点击菜单消息，触发的回复消息中附带的菜单ID
     */
    @JsonProperty("menu_id")
    private String menuId;
}
