package com.guosen.ewas.wechat.jxh.domain;

import java.time.LocalDateTime;
import lombok.Data;

/**
    * 投资建议
    */
@Data
public class TWxoaThjlTzjy {
    /**
    * 流水号
    */
    private Long lsh;

    /**
    * 消息ID，存档接口返回，这个有索引，根据这个查
    */
    private String msgid;

    /**
    * 投资建议ID
    */
    private String tzjyId;

    /**
    * 投资建议名称
    */
    private String tzjyMc;

    /**
    * 消息seqid，存档接口返回，如果是微信客服则没有这个id
    */
    private Long seqid;

    /**
    * 1存档接口，2微信客服
    */
    private Integer sjly;

    /**
    * 最后修改日期
    */
    private LocalDateTime updatetime;
}