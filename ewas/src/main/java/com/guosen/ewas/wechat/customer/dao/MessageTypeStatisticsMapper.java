package com.guosen.ewas.wechat.customer.dao;

import com.guosen.ewas.wechat.customer.domain.bo.SearchMessageTypeStatistics;
import com.guosen.ewas.wechat.customer.domain.entity.MessageTypeStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MessageTypeStatisticsMapper {
    /**
     * 根据 id 删除
     *
     * @param id id
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     *
     * @param record 实体
     * @return 影响行数
     */
    int insert(MessageTypeStatistics record);

    /**
     * 插入
     *
     * @param record 实体
     * @return 影响行数
     */
    int insertSelective(MessageTypeStatistics record);

    /**
     * 根据 id 查询
     *
     * @param id id
     * @return 员工实体
     */
    MessageTypeStatistics selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record 实体
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(MessageTypeStatistics record);

    /**
     * 更新
     *
     * @param record 实体
     * @return 影响行数
     */
    int updateByPrimaryKey(MessageTypeStatistics record);

    /**
     * 叠加更新
     *
     * @param upt 新增内容
     * @return 结果
     */
    int updateByPrimaryKeyPlus(@Param("upt") MessageTypeStatistics upt);

    /**
     * 根据条件查询实体
     *
     * @param userid    企业微信 userid
     * @param localDate 创建时间
     * @param type      类型
     * @return 具体统计实体
     */
    MessageTypeStatistics selectByUseridAndCreatedDateAndType(@Param("userid") String userid, @Param("date") LocalDate localDate, @Param("type") Integer type);

    /**
     * 根据条件查询列表
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userids   企业微信 userid 集合
     * @return 集合
     */
    List<SearchMessageTypeStatistics> selectAllByCreateDateBetweenAndUserId(@Param("startDate") LocalDate startDate, @Param("endDate")
            LocalDate endDate, @Param("userids") Collection<String> userids);

}
