package com.guosen.ewas.wechat.jxh.dao;

import com.guosen.ewas.wechat.jxh.domain.TWxoaThjlTzjy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TWxoaThjlTzjyMapper {
    TWxoaThjlTzjy selectByPrimaryKey(Long lsh);

    List<TWxoaThjlTzjy> selectByMsgidIn(@Param("msgidCollection") Collection<String> msgidCollection);

}