package com.guosen.ewas.wechat.job;

import com.guosen.ewas.wechat.common.redis.RedisDistributedLockHelper;
import com.guosen.ewas.wechat.customer.service.CustomerSyncService;
import com.guosen.ewas.wechat.uc.service.UserService;
import com.guosen.zebra.distributed.lock.DistributedLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时同步员工部门数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserSyncJob {
    private final UserService userService;
    private final CustomerSyncService customerSyncService;

    @Scheduled(cron = "0 0 2 * * ?")
    public void sync() {
        DistributedLock lock = RedisDistributedLockHelper.getLock("UserSyncJob", 60 * 60 * 3L);
        if (lock == null || !lock.tryLock()) {
            log.debug("定时同步员工部门数据 获取锁失败");
            return;
        }
        try {
            // 同步员工
            log.info("Sync user.");
            userService.sync();
            // 同步客户（外部联系人）
            //根据员工获取所有需要同步的客户external_userid
            log.info("Sync customer by user start");
            customerSyncService.sync();
            log.info("Sync customer by user end");
        } catch (Exception e) {
            log.error("定时同步员工部门数据 任务出错", e);
        } finally {
            lock.unlock();
        }
    }
}
