package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.ChatDataWrap;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/29 15:12
 */
public class GetChatDataDTO {

    @JsonProperty("errcode")
    private Integer errcode;
    @JsonProperty("errmsg")
    private String errmsg;
    @JsonProperty("chatdata")
    private List<ChatDataWrap> chatdata;

    @Override
    public String toString() {
        return "GetChatDataDTO{" +
                "errcode=" + errcode +
                ", errmsg='" + errmsg + '\'' +
                ", chatdata=" + chatdata +
                '}';
    }

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public List<ChatDataWrap> getChatdata() {
        return chatdata;
    }

    public void setChatdata(List<ChatDataWrap> chatdata) {
        this.chatdata = chatdata;
    }

}
