package com.guosen.ewas.wechat.weixin.cp.dto.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.weixin.cp.dto.BaseWxDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 内部群信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MsgauditGroupchatDTO extends BaseWxDTO {
    private String roomname;
    private String creator;
    private String notice;
    @JsonProperty("room_create_time")
    private Long roomCreateTime;
    private List<MsgauditGroupMember> members;
}
