package com.guosen.ewas.wechat.weixin.cp;

import com.guosen.ewas.wechat.weixin.cp.dto.agent.AgentInfoDTO;
import com.guosen.ewas.wechat.weixin.cp.enums.MpParamEnum;
import com.guosen.ewas.wechat.weixin.cp.service.ICpBaseService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 应用管理
 *
 * <AUTHOR>
 */
@Component
public class CpAgentServiceImpl implements ICpAgentService {
    @Resource
    ICpBaseService mpBaseService;

    @Override
    public AgentInfoDTO get(String agentId) {
        HashMap<String, Object> map = new HashMap<>(1);
        map.put("agentid", agentId);
        return mpBaseService.get(MpParamEnum.AGENT_GET, map, AgentInfoDTO.class);
    }
}
