package com.guosen.ewas.wechat.sensitive.domain.bo;

import com.guosen.ewas.wechat.common.domain.EmpInfo;
import lombok.Data;

import java.util.List;

/**
 * 检测敏感词-bo对象
 *
 * <AUTHOR>
 * @date 2021/9/7 10:42
 */
@Data
public class SensitiveRuleCheckBO {
    /**
     * id
     */
    private Long id;
    /**
     * 规则名
     */
    private String ruleName;

    /**
     * 敏感词触发类型 1:仅员工触发,2仅客户触发,3:员工和客户都能触发
     */
    private Integer sensitiveTriggerType;

    /**
     * 是否员工间能触发 1：是，0：否
     */
    private Integer isBetweenUser;

    /**
     * 规则对应通知人
     */
    private List<EmpInfo> users;

    private List<SenWordBO> words;

}
