package com.guosen.ewas.wechat.customer.dao;
import java.time.LocalDateTime;

import com.guosen.ewas.wechat.common.domain.GroupInfo;
import com.guosen.ewas.wechat.customer.domain.bo.GroupStatisticsBO;
import com.guosen.ewas.wechat.customer.domain.bo.UpdateLastMsgForGroupBO;
import com.guosen.ewas.wechat.customer.domain.entity.GroupChat;
import com.guosen.ewas.wechat.customer.domain.vo.GroupByMemberCustomerPageReqVO;
import com.guosen.ewas.wechat.customer.domain.vo.GroupByMemberUserPageReqVO;
import com.guosen.ewas.wechat.customer.domain.vo.LastMsgGroupVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 客户群Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupChatMapper {
    /**
     * 查询客户群
     *
     * @param id 客户群ID
     * @return 客户群
     */
    GroupChat selectById(Long id);

    /**
     * 通过群的chat_id查询客户群
     *
     * @param chatId 群的chat_id
     * @return 客户群
     */
    GroupChat selectByChatId(String chatId);

    /**
     * 新增客户群
     *
     * @param groupChat 客户群
     * @return 结果
     */
    int insert(GroupChat groupChat);

    /**
     * 批量新增客户群
     *
     * @param groupChat 客户群
     * @return 结果
     */
    int insertBatch(List<GroupChat> groupChat);

    /**
     * 修改客户群
     *
     * @param groupChat 客户群
     * @return 结果
     */
    int update(GroupChat groupChat);

    /**
     * 查询200人以上的群数量
     *
     * @param userIds 员工id集合
     * @return 结果
     */
    int selectMemberCountLgTwoHCount(@Param("userIds") List<Long> userIds);

    /**
     * 统计客户群数据
     *
     * @return 结果
     */
    List<GroupStatisticsBO> selectStatisticsByGroup();

    /**
     * 需要同步群的群主userid集合
     *
     * @return 群主的userid
     */
    List<String> selectNeedSyncGroupOwnerids();

    /**
     * 员工检索-群列表
     *
     * @param reqVO 条件
     * @return 结果
     */
    List<LastMsgGroupVO> selectGroupByMemberUser(GroupByMemberUserPageReqVO reqVO);

    /**
     * 客户检索-群列表
     *
     * @param reqVO 条件
     * @return 结果
     */
    List<LastMsgGroupVO> selectGroupByMemberCustomer(GroupByMemberCustomerPageReqVO reqVO);

    /**
     * 通过群id查询群信息
     *
     * @param groupId 群id
     * @return 结果
     */
    GroupInfo selectInfoByGroupId(@Param("groupId") Long groupId);

    /**
     * 通过群id查询群信息
     *
     * @param chatId 群的chatid
     * @return 结果
     */
    GroupInfo selectInfoByChatId(@Param("chatId") String chatId);

    /**
     * 检测人员是否在群中
     *
     * @param chatid 群的chatid
     * @param userid 人员的userid
     * @param type   类型：1-员工，2-客户
     * @return 结果
     */
    Integer checkGroupScope(@Param("chatid") String chatid, @Param("userid") String userid, @Param("type") Integer type);

    /**
     * 更新会话xiaxo
     *
     * @param bos
     */
    void updateLastMsg(@Param("bos") List<UpdateLastMsgForGroupBO> bos);

    /**
     * selectByOwnerIdList
     *
     * @param userIds 用户id集
     */
    Set<Long> selectByOwnerIdList(@Param("userIds") Set<Long> userIds);

    List<GroupChat> selectByIdIn(@Param("idCollection") Collection<Long> idCollection);

    int insertOrUpdateSelective(GroupChat record);

    List<GroupChat> selectByAll(GroupChat groupChat);


}
