package com.guosen.ewas.wechat.common.constant;

import com.guosen.ewas.wechat.msg.enums.ChatType;

/**
 * redis key常量
 *
 * <AUTHOR>
 * @date 2021/9/4 16:08
 */
public interface RedisKeyConsts {
    /**
     * 撤回消息队列
     */
    String RECALL_PRE = "recall:preMsgid";

    /**
     * 撤回消息内容:key->消息msgid,value->次数
     */
    String RECALL_PRE_LIST = "recall:preMsgid:list:";

    /**
     * 会话内容队列
     */
    String MSG_LIST = "wechat:msg:list";

    /**
     * 数据修复的列表
     */
    String MSG_MIGRATED_LIST = "wechat:msg:migrated:list";

    /**
     * log:msg:类型:是否存在客户:员工ID:客户ID
     */
    String MSG_LAST_LOG_SINGLE_PRE = ChatType.SINGLE.getCode() + ":{}:{}:{}";
    /**
     * log:msg:群类型:群ID
     */
    String MSG_LAST_LOG_GROUP_PRE = ChatType.ROOM.getCode() + ":{}";

    /**
     * 消息记录-存在客户
     */
    Integer MSG_LOG_EXIST_C = 1;
    /**
     * 消息记录-不存在客户
     */
    Integer MSG_LOG_NOT_EXIST_C = 0;

    /**
     * 需要同步的员工userid
     */
    String SYNC_NEED_USERID = "sync:need:userid:list";

    /**
     * 需要同步的客户的external_user
     */
    String SYNC_CUSTOMER_EXTERNALUSERID = "sync:customer:externalUserid:list";

    /**
     * 需要同步的客户群的chatid
     */
    String SYNC_GROUP_CHATID = "sync:group:chatid:list";

    /**
     * 不存在的人（不在范围内的人）同步失败缓存前缀
     */
    String SYNC_NOTFOUND_ERROR = "sync:notfound:error:";

    /**
     * 不存在的客户群同步失败缓存前缀
     */
    String SYNC_NOTFOUND_GROUP_ERROR = "sync:notfound:group:error:";

    /**
     * 员工与客户的关系：员工id-客户id
     */
    String STORAGE_RELATION_SET = "storage:relation:set";

    /**
     * 缓存员工userid
     */
    String STORAGE_USER_LIST = "storage:userid:list";

    /**
     * 缓存客户external_userid
     */
    String STORAGE_CUSTOMER_LIST = "storage:externalUserid:list";

    /**
     * 缓存客户群的chatid
     */
    String STORAGE_GROUP_LIST = "storage:chatid:list";

    /**
     * 锁通用锁定时间：1分钟
     */
    Long LOCK_COMM_TIME = 60L;

    /**
     * 系统初始化
     */
    String LOCK_SYSTEM_INIT = "lock:system:init";

    /**
     * 同步客户锁
     */
    String LOCK_SYNC_CUSTOMER = "lock:sync:customer:";

    /**
     * 新增聊天记录
     */
    String LOCK_ADD_MSG_LOG = "lock:add:msg:log:";

    /**
     * 会话类别统计锁
     */
    String LOCK_MSG_TYPE_STATS = "lock:msg:type:stats:";

    String LOCK_MSG_REPAIR = "lock:msg:repair";

    /**
     * api请求限制
     */
    String API_LIMIT = "api:limit:";

    /**
     * 电脑运行的ip，判断启动了几台机器
     */
    String RUNTIME_IP = "runtime:ip";

    String EXPORT_DOWNLOAD = "export:download:";
    /**
     * 文件转 PDF 缓存
     */
    String FILE_PDF = "file:pdf:";
    String FILE_PDF_KF = "file:pdf:kf:";

    /**
     * redis缓存失效时间
     */
    interface TimeOut {
        /**
         * 一分钟
         */
        long ONE_MINUTE = 60;
        /**
         * 三天
         */
        long THREE_DAY = 259200;
        /**
         * 一天
         */
        long ONE_DAY = 86400;
        /**
         * 10小时
         */
        long TEN_HOURS = 36000;
        /**
         * 30天
         */
        long THIRTY_DAY = 2592000;
        /**
         * 一小时
         */
        long ONE_HOUR = 3600;
        /**
         * 三小时
         */
        long THREE_HOUR = 10800;
        /**
         * 五分钟
         */
        long FIVE_MINUTES = 300;
        /**
         * 十分钟
         */
        long TEN_MINUTES = 600;
        /**
         * 30分钟
         */
        long THIRTY_MINUTES = 1800;

    }

    /**
     * 定时器未接入国信分布式定时器系统。 <br/>
     * 集群，定时器涉及同步问题，因此增加锁来处理
     */
    interface JobLock {

        String REPLY_TIMEOUT_STATS = "job:lock:reply:timeout:stats";

        // 敏感词统计定时任务
        String SENSITIVE_STATISTICS_LOCK = "job:lock:sensitive:statistics";

        // 敏感词统计定时任务
        String SENSITIVE_STATISTICS_DATE_LOCK = "job:lock:sensitive:statistics:date:";

        //单聊数据拉取定时任务
        String SINGLE_CHAT_STATISTICS_DATE_LOCK = "job:lock:single:chat:statistics";

        //群聊数据拉取定时任务
        String GROUP_CHAT_STATISTICS_DATE_LOCK = "job:lock:group:chat:statistics";

        //错误消息
        String MSG_ERROR = "job:lock:msg:error";

        //处理撤回消息
        String MSG_SYNC = "job:lock:msg:sync";

        //处理撤回消息
        String MSG_RECALL = "job:lock:msg:recall";

        // 消息拉去
        String MSG_PULL = "job:lock:msg:pull";
        String MSG_PULL_SEGMENT = "job:lock:msg:pull:segment";

        String REJOIN_QUEUE = "job:lock:msg:rejoin:queue";

        //语言转文本
        String MSG_VOICE_RESULT = "job:lock:msg:voice:result";

        // 文件上传至oss锁
        String ERROR_OSS_FILES = "job:lock:file:oss:error";

        // 文件下载锁
        String ERROR_DOWNLOAD_FILES = "job:lock:file:download:error";

        // 文件下载锁2
        String ERROR_DOWNLOAD_FILES_2 = "job:lock:file:download:error2";

        // 消息记录备份,初始化表
        String MSG_BACKUP_ADD = "job:lock:msg:backup:add";

        // 消息记录备份，更新范围记录
        String MSG_BACKUP_UPD = "job:lock:msg:backup:upd";

        // 数据迁移job
        String DATA_MIGRATED = "job:lock:data:migrated";

        // 健康检测
        String HEALTH = "job:lock:health";

        String SYNC_VIRTUAL_PEOPLE = "job:lock:sync:virtual";
        String STATS_SCORE = "job:lock:stats:score";

    }

    /**
     * 回复超时相关使用redis的key值
     */
    interface ReplyTimeout {

        /**
         * 超时回复使用的消息队列
         **/
        String MSG_QUEUE = "replyTimeout:msg:queue";

        /**
         * 员工与客户之间最后的消息缓存Map
         * replyTimeout:msg:userId:customerId
         * 示例：replyTimeout:msg:1:2
         */
        String MSG_LAST = "replyTimeout:msg:{}:{}";

        /**
         * 缓存消息的map的hashKey的set
         */
        String MSG_SET_HASHKEY = "replyTimeout:msg:hashKey";

        /**
         * 接受消息处理锁：
         * {}:{}: userId:customerId
         */
        String LOCK_MSG = "replyTimeout:lock:msg:{}:{}";

        long TIMEOUT = 24 * 60 * 60L;

        String ID_SPLIT = "-";

        /**
         * 配置
         */
        String SETTING = "replyTimeout:set";

        /**
         * 数据统计标识位
         * {}：日期，示例：20210923
         */
        String STATS_FLAG = "replyTimeout:stats:{}";

    }

    interface Health {

        /**
         * 半小时
         */
        long TIMEOUT = 1800;

        String MSG_PULL_RESULT = "msg:pullResult";

        /**
         * 消息存储到 MySQL
         **/
        String MSG_MYSQL = "msg:mysql";

        /**
         * 消息处理
         */
        String MSG_STATE = "msg:state";

        /**
         * 消息存储到 Elasticsearch
         **/
        String MSG_ES = "msg:es:{}";

        Integer SUCCESS = 0;

        Integer FAIL = 1;
    }

}
