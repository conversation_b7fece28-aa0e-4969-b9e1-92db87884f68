package com.guosen.ewas.wechat.customer.domain.bo;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/4/9 15:50
 */
public class UpdateLastMsgForRelationBO {
    private Long userId;

    private Long customerId;

    private LocalDateTime lastMsgTime;

    private String lastMsg;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public LocalDateTime getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(LocalDateTime lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    @Override
    public String toString() {
        return "UpdateLastMsgForRelationBO{" +
                "userId=" + userId +
                ", customerId=" + customerId +
                ", lastMsgTime=" + lastMsgTime +
                ", lastMsg='" + lastMsg + '\'' +
                '}';
    }
}
