package com.guosen.ewas.wechat.uc.domain.vo;

/**
 * <AUTHOR>
 */
public class DepartmentUsersVO {
    private Long id;
    private String userid;
    private String name;
    //    private String avatar;
    private Integer sort;
    private Integer state;
    private Long deptId;
    /**
     * 是否开通了聊天回话存档
     */
    private Integer messageUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

//    public String getAvatar() {
//        return avatar;
//    }
//
//    public void setAvatar(String avatar) {
//        this.avatar = avatar;
//    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Integer getMessageUser() {
        return messageUser;
    }

    public void setMessageUser(Integer messageUser) {
        this.messageUser = messageUser;
    }

    @Override
    public String toString() {
        return "DepartmentUsersVO{" +
                "id=" + id +
                ", userid='" + userid + '\'' +
                ", name='" + name + '\'' +
                ", sort=" + sort +
                ", state=" + state +
                ", deptId=" + deptId +
                ", messageUser=" + messageUser +
                '}';
    }
}
