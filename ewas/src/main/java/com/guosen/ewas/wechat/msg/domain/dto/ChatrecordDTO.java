package com.guosen.ewas.wechat.msg.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.guosen.ewas.wechat.msg.domain.wrap.ChatRecordWrap;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/1/5 11:22
 * 消息为：chatrecord。String类型
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChatrecordDTO extends DataBaseDTO {

    /**
     * chatrecord
     */
    @JsonProperty("chatrecord")
    private ChatRecordWrap chatrecord;

}
