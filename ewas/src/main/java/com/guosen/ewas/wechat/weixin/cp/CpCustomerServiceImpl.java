package com.guosen.ewas.wechat.weixin.cp;


import com.guosen.ewas.wechat.weixin.cp.dto.BaseWxDTO;
import com.guosen.ewas.wechat.weixin.cp.dto.ContactWay.*;
import com.guosen.ewas.wechat.weixin.cp.dto.customer.*;
import com.guosen.ewas.wechat.weixin.cp.enums.MpParamEnum;
import com.guosen.ewas.wechat.weixin.cp.service.ICpBaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Service
public class CpCustomerServiceImpl implements ICpCustomerService {

    /**
     * 无权限操作标签
     */
    private final static Integer NO_PERMISSION_OPERATION_TAG = 81011;

    @Resource
    ICpBaseService mpBaseService;

    @Override
    public FollowUserListDTO getFollowUserList() {
        return mpBaseService.get(MpParamEnum.EC_USER_FOLLOW_LIST, null, FollowUserListDTO.class);
    }

    @Override
    public BaseWxDTO remark(RemarkDTO dto) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_REMARK, dto, BaseWxDTO.class);
    }

    @Override
    public DetailDTO detail(String externalUserid) {
        HashMap<String, Object> map = new HashMap<>(1);
        map.put("external_userid", externalUserid);
        return mpBaseService.get(MpParamEnum.EC_GET, map, DetailDTO.class);
    }

    @Override
    public ListDTO list(String userid) {
        HashMap<String, Object> map = new HashMap<>(1);
        map.put("userid", userid);
        return mpBaseService.get(MpParamEnum.EC_LIST, map, ListDTO.class);
    }

    @Override
    public BatchDTO batch(BatchReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_BATCH_GET_BY_USER, req, BatchDTO.class);
    }

    @Override
    public CorpTagListDTO getCorpTagList(CorpTagListReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_CORP_TAG_GET, req, CorpTagListDTO.class);
    }

    @Override
    public AddCorpTagDTO addCorpTag(TagGroup req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_CORP_TAG_ADD, req, AddCorpTagDTO.class);
    }


    @Override
    public BaseWxDTO editCorpTag(TagGroup.Tag req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_CORP_TAG_EDIT, req, BaseWxDTO.class);
    }

//    @Override
//    public BaseWxDTO delCorpTag(DelCorpTagReq req) {
//        BaseWxDTO dto = mpBaseService.postJsonBody(MpParamEnum.EC_CORP_TAG_DEL, req, BaseWxDTO.class);
//        if (null != dto && NO_PERMISSION_OPERATION_TAG.equals(dto.getErrcode())) {
//            dto = mpBaseService.postJsonBody(MpParamEnum.EC_CORP_TAG_DEL_CUSTOMER, req, BaseWxDTO.class);
//        }
//        return dto;
//    }

    @Override
    public BaseWxDTO markTag(MarkTagReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_MARK_TAG, req, BaseWxDTO.class);
    }


    @Override
    public UnassignedList getUnassignedList(UnassignedListReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_UNASSIGNED_LIST, req, UnassignedList.class);
    }

    @Override
    public BaseWxDTO transfer(TransferReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_TRANSFER, req, BaseWxDTO.class);
    }

    @Override
    public TransferResultDTO getTransferResult(TransferReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_TRANSFER_RESULT, req, TransferResultDTO.class);
    }

    @Override
    public GroupChatTransferDTO groupChatTransfer(GroupChatTransferReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GROUP_CHAT_TRANSFER, req, GroupChatTransferDTO.class);
    }

    @Override
    public GroupChatListDTO groupChatList(GroupChatListReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GROUP_CHAT_LIST, req, GroupChatListDTO.class);
    }

    @Override
    public GroupChatDetailDTO groupChatDetail(GroupChatDetailReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GROUP_CHAT_GET, req, GroupChatDetailDTO.class);
    }

    @Override
    public BaseWxDTO sendWelcomeMsg(SendWelcomeMsgReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_SEND_WELCOME_MSG, req, BaseWxDTO.class);
    }

    @Override
    public UserBehaviorDataDTO getUserBehaviorData(UserBehaviorDataReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_USER_BEHAVIOR_GET, req, UserBehaviorDataDTO.class);
    }

    @Override
    public ContactWayDTO getContactWay(DeleteContactWayReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GET_CONTACT_WAY, req, ContactWayDTO.class);
    }

    @Override
    public AddContactWayDTO addContactWay(AddContactWayReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_ADD_CONTACT_WAY, req, AddContactWayDTO.class);
    }

    @Override
    public BaseWxDTO updateContactWay(UpdateContactWayReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_UPDATE_CONTACT_WAY, req, BaseWxDTO.class);
    }

    @Override
    public BaseWxDTO deleteContactWay(DeleteContactWayReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_DEL_CONTACT_WAY, req, BaseWxDTO.class);
    }

    @Override
    public GroupMsgTaskDTO getGroupmsgTask(GroupMsgTaskReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GET_GROUPMSG_TASK, req, GroupMsgTaskDTO.class);
    }

    @Override
    public GroupChatStatisticDTO getGroupChatStatistic(GroupChatStatisticReq req) {
        return mpBaseService.postJsonBody(MpParamEnum.EC_GET_GROUPCHAT_STATISTICS, req, GroupChatStatisticDTO.class);
    }
}
