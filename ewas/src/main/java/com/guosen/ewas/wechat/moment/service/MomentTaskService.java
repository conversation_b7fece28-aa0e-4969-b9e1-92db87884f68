package com.guosen.ewas.wechat.moment.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.moment.dao.MomentTaskMapper;
import com.guosen.ewas.wechat.moment.domain.MomentCountBO;
import com.guosen.ewas.wechat.moment.domain.entity.MomentTask;
import com.guosen.ewas.wechat.moment.domain.vo.MomentTaskReqVO;
import com.guosen.ewas.wechat.moment.domain.vo.MomentTaskVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MomentTaskService {

    private final MomentTaskMapper momentTaskMapper;
    private final MomentCommentsService commentsService;
    private final MomentVisibleStatsService visibleStatsService;

    public int insertOrUpdateBatch(Collection<MomentTask> records) {
        if (CollUtil.isEmpty(records)) {
            return 0;
        }
        // records 根据 momentId 分组
        Map<String, List<MomentTask>> groupedRecords = records.stream().collect(Collectors.groupingBy(MomentTask::getMomentId));
        groupedRecords.forEach((momentId, momentTasks) -> {
            // 通过 momentId 查询是否存在记录,不存在批量新增
            // 存在更新状态
            if (null == momentTaskMapper.selectOneByMomentId(momentId)) {
                momentTaskMapper.insertBatch(momentTasks);
            } else {
                momentTasks.forEach(i -> {
                            MomentTask task = momentTaskMapper
                                    .selectOneByMomentIdAndUserid(i.getMomentId(), i.getUserid());
                            if (null == task) {
                                momentTaskMapper.insertSelective(i);
                            } else if (!Objects.equals(task.getPublishStatus(), i.getPublishStatus())) {
                                momentTaskMapper.updateStatusByMomentIdAndUserid(i.getMomentId(), i.getUserid(), i.getPublishStatus());
                            }
                        }
                );
            }
        });
        return groupedRecords.size();
    }

    public List<MomentCountBO> groupByMomentIds(Collection<String> momentIds) {
        List<MomentCountBO> momentCountBOS = momentTaskMapper.groupByMomentIds(momentIds);
        return null == momentCountBOS ? Collections.emptyList() : momentCountBOS;
    }


    public PageWrapVO<MomentTaskVO> page(PageWrapVO<MomentTaskReqVO> page) {
        MomentTaskReqVO search = null == page.getSearch() ? new MomentTaskReqVO() : BeanUtil.copyProperties(page.getSearch(), MomentTaskReqVO.class);
        PageInfo<MomentTaskVO> pageInfo = PageMethod.startPage(page.getPageNum(), page.getPageSize()).setOrderBy("modify_datetime desc")
                .doSelectPageInfo(() -> momentTaskMapper.selectByMomentTaskReq(search));
        PageWrapVO<MomentTaskVO> result = BeanUtil.copyProperties(pageInfo, PageWrapVO.class);
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            List<MomentTaskVO> momentVOList = new ArrayList<>(pageInfo.getList().size());
            pageInfo.getList().forEach(m -> {
                MomentTaskVO vo = BeanUtil.copyProperties(m, MomentTaskVO.class);
                Optional.ofNullable(commentsService.groupByMomentIdAndCreator(m.getMomentId(), m.getUserid()))
                        .ifPresent(i -> {
                            vo.setCommentCount(i.getCommentCount());
                            vo.setLikeCount(i.getLikeCount());
                        });
                Optional.ofNullable(visibleStatsService.getMomentIdAndUserId(m.getMomentId(), m.getUserid()))
                        .ifPresent(i -> {
                            vo.setVisibleCount(i.getVisibleCount());
                            vo.setCustomerVisibleCount(i.getCustomerCount());
                        });
                momentVOList.add(vo);
            });
            result.setList(momentVOList);
        }
        return result;
    }

}
