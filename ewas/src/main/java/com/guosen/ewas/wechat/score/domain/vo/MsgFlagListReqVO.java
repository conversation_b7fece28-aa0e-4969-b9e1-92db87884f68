package com.guosen.ewas.wechat.score.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.guosen.ewas.wechat.uc.domain.bo.ScopeBO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class MsgFlagListReqVO {
    private Long flagType;
    private String keyWord;
    private ScopeBO scope;
    private Set<Long> creatorIds;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;
}
