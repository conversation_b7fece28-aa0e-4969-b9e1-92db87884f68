package com.guosen.ewas.wechat.msg.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.constant.DateConsts;
import com.guosen.ewas.wechat.common.constant.FileConsts;
import com.guosen.ewas.wechat.common.constant.RedisKeyConsts;
import com.guosen.ewas.wechat.common.domain.CustomerInfo;
import com.guosen.ewas.wechat.common.domain.EmpInfo;
import com.guosen.ewas.wechat.common.domain.PageWrapVO;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.ewas.wechat.common.util.DateUtils;
import com.guosen.ewas.wechat.common.util.JacksonUtils;
import com.guosen.ewas.wechat.common.util.RedisUtils;
import com.guosen.ewas.wechat.common.util.UserContext;
import com.guosen.ewas.wechat.customer.domain.entity.Customer;
import com.guosen.ewas.wechat.customer.domain.entity.GroupChat;
import com.guosen.ewas.wechat.customer.service.CustomerService;
import com.guosen.ewas.wechat.customer.service.GroupChatService;
import com.guosen.ewas.wechat.msg.config.MsgSearchPool;
import com.guosen.ewas.wechat.msg.domain.bo.CustomerSearchBO;
import com.guosen.ewas.wechat.msg.domain.bo.MessageBO;
import com.guosen.ewas.wechat.msg.domain.bo.MsgInfoQueryBO;
import com.guosen.ewas.wechat.msg.domain.entity.MsgInfo;
import com.guosen.ewas.wechat.msg.domain.entity.MsgLast;
import com.guosen.ewas.wechat.msg.domain.vo.GlobalMsgVO;
import com.guosen.ewas.wechat.msg.domain.vo.MessageVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgPeopleVO;
import com.guosen.ewas.wechat.msg.domain.vo.MsgPositionVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgGlobalReqVO;
import com.guosen.ewas.wechat.msg.domain.vo.req.MsgSingleReqVO;
import com.guosen.ewas.wechat.msg.enums.MessageEnum;
import com.guosen.ewas.wechat.msg.enums.SendTypeEnum;
import com.guosen.ewas.wechat.msg.enums.SenderType;
import com.guosen.ewas.wechat.msg.service.*;
import com.guosen.ewas.wechat.msg.strategy.Strategy;
import com.guosen.ewas.wechat.msg.tools.MsgUtils;
import com.guosen.ewas.wechat.uc.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 会话检索服务
 *
 * <AUTHOR> Peijun
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MsgSearchServiceImpl implements IMsgSearchService {
    private final UserService userService;
    private final CustomerService customerService;
    private final GroupChatService groupChatService;
    private final IMsgInfoService msgInfoService;
    private final MsgToolsService msgToolsService;
    private final IMsgLastService msgLastService;
    private final StrategyContainer strategyContext;
    private final IMsgHandlerService msgHandlerService;
    private final IFileInfoService fileInfoService;
    private final MsgFileService msgFileService;
    private final ConfigKvService configKvService;

    @Override
    public MessageBO selectByMsgId(String msgId) {
        log.debug("select by msgId:{}", msgId);
        if (CharSequenceUtil.isEmpty(msgId)) {
            return null;
        }
        MsgInfo msgInfo = msgInfoService.getByMsgId(msgId);
        if (null != msgInfo) {
            // 数据未解析过，调用解析方法
            if (StrUtil.isBlank(msgInfo.getRelation())) {
                return msgHandlerService.parseChatData(msgInfo);
            } else {
                MessageBO bo = BeanUtil.copyProperties(msgInfo, MessageBO.class);
                MsgLast last = msgLastService.getByRelation(bo.getRelation());
                bo.setUserId(last.getUserId());
                bo.setUserIdMax(last.getUserIdMax());
                bo.setGroupId(last.getGroupId());
                bo.setCustomerId(last.getCustomerId());
                SendTypeEnum sendTypeEnum = bo.getSendTypeEnum();
                // 发送人
                if (SenderType.CUSTOMER.getCode().equals(sendTypeEnum.getFromType())) {
                    Optional.ofNullable(customerService.selectOneById(bo.getFromId())).ifPresent(i -> {
                        bo.setFrom(i.getExternalUserid());
                        bo.setFromName(i.getName());
                    });
                } else if (SenderType.USER.getCode().equals(sendTypeEnum.getFromType())) {
                    Optional.ofNullable(userService.selectByPrimaryKey(bo.getFromId())).ifPresent(i -> {
                        bo.setFrom(i.getUserid());
                        bo.setFromName(i.getName());
                    });
                }
                // 接收对象
                if (SenderType.CUSTOMER.getCode().equals(sendTypeEnum.getReceiveType())) {
                    Optional.ofNullable(customerService.selectOneById(bo.getReceiveId())).ifPresent(i -> {
                        bo.setReceive(i.getExternalUserid());
                        bo.setReceiveName(i.getName());
                    });
                } else if (SenderType.USER.getCode().equals(sendTypeEnum.getReceiveType())) {
                    Optional.ofNullable(userService.selectByPrimaryKey(bo.getReceiveId())).ifPresent(i -> {
                        bo.setReceive(i.getUserid());
                        bo.setReceiveName(i.getName());
                    });
                } else if (SenderType.GROUP.getCode().equals(sendTypeEnum.getReceiveType())) {
                    Optional.ofNullable(groupChatService.selectOneById(bo.getReceiveId())).ifPresent(i -> {
                        bo.setReceive(i.getChatId());
                        bo.setReceiveName(i.getName());
                        bo.setGroupOwnerId(i.getOwnerId());
                    });
                }
                return bo;
            }
        }
        return null;
    }

    @Override
    public List<MessageVO> selectSingle(MsgSingleReqVO search) {
        log.debug("Message single {}", search);
        //  兼容旧的逻辑，群聊时GroupId必传
        String relation = msgToolsService.buildRelation(search.getFromId(), search.getReceiveId(),
                SendTypeEnum.valueOf(search.getSendType()));
        MsgLast msgLastLog = msgLastService.getByRelation(relation);
        if (null == msgLastLog) {
            //  找不到最后聊天记录，则彼此间无会话数据
            return Collections.emptyList();
        }
        // 设置默认值
        search.setPageUp(null == search.getPageUp() || search.getPageUp());
        MsgInfoQueryBO query = new MsgInfoQueryBO();
        query.setPageUp(search.getPageUp());
        query.setPageSize(search.getPageSize());
        query.setContent(search.getContent());
        query.setExcludeContent(search.getExcludeContent());
        query.setMsgType(MessageEnum.OTHER.getCode().equals(search.getType()) ? null : search.getType());
        query.setRelation(msgLastLog.getRelation());
        //  构建查询时间
        buildQueryTime(search.getStartTime(), search.getEndTime(), msgLastLog.getMsgTimestamp(),
                DateUtils.date2Milli(msgLastLog.getCreateDatetime()), query);
        buildMsgFlag(search.getMsgId(), query.getPageUp(), query);

        contentSet(query);
        List<MsgInfo> msgInfos = unionData(query, MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()),
                DateUtils.ms2Date(query.getEndTimestamp())));
        List<MessageVO> result = new ArrayList<>(msgInfos.size());
        if (CollUtil.isNotEmpty(msgInfos)) {
            msgInfos.stream().sorted(Comparator.comparing(MsgInfo::getMsgTimestamp))
                    .forEach(i -> result.add(msgInfo2MsgVO(i)));
        }
        return result;
    }

    private List<MsgInfo> unionData(MsgInfoQueryBO query, List<String> allTables) {
        log.debug("Msg query union data allTables = {},{}", allTables, query);
        int total = query.getPageSize();
        List<MsgInfo> result = new ArrayList<>(total);
        // query.getPageUp() = true 查询过去数据，那么优先检测近的表：202308->202307->202306
        // 上翻页的时候，需要调整为根据时间正序，倒叙查询是以截止时间判断的。
        allTables = query.getPageUp()
                ? allTables.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList())
                : allTables.stream().sorted().collect(Collectors.toList());
        for (String table : allTables) {
            query.setTableName(table);
            query.setPageSize(total - result.size());
            List<MsgInfo> msgInfos = msgInfoService.selectByQuery(query);
            result.addAll(msgInfos);
            if (result.size() >= total) {
                break;
            }
        }
        return result;
    }

    /**
     * 时间参数构建
     *
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param maxMsgTime 消息的最后时间（最大）
     * @param minMsgTime 消息的开始时间（最小）
     * @param query      查询参数
     */
    private void buildQueryTime(Long startTime, Long endTime, Long maxMsgTime, Long minMsgTime, MsgInfoQueryBO query) {
        //  查询时间不存在，采用最后记录表的开始和结束时间
        query.setStartTimestamp(startTime == null ? minMsgTime : Math.max(startTime, minMsgTime));
        query.setEndTimestamp(null == endTime ? maxMsgTime : Math.min(endTime, maxMsgTime));
    }

    private void buildMsgFlag(String msgId, Boolean pageUp, MsgInfoQueryBO query) {
        MsgInfo msgInfo = msgInfoService.getByMsgId(msgId);
        if (null != msgInfo) {
            long timestamp = msgInfo.getMsgTimestamp() != null ? msgInfo.getMsgTimestamp() :
                    DateUtils.date2Milli(msgInfo.getCreateDateTime());
            //  不重复取数据
            if (pageUp) {
                //  找历史数据
                if (query.getEndTimestamp() > timestamp) {
                    query.setEndTimestamp(timestamp - 1);
                }
            } else {
                //  找未来数据
                if (query.getStartTimestamp() < timestamp) {
                    query.setStartTimestamp(timestamp + 1);
                }
            }
        }
    }

    @Override
    public List<GlobalMsgVO> selectGlobal(MsgGlobalReqVO search) {
        if (null == search) {
            return Collections.emptyList();
        }
        // 设置默认值
        search.setPageUp(null == search.getPageUp() || search.getPageUp());
        //  数据权限，根据部门集合查询出员工ID合集
        Set<Long> userList = userService.getAndCheckUserIds(search.getScope());
        //  不指定单聊/群聊查询、不指定群的情况下，获取权限范围内的群
//        Set<Long> groupIds = !Boolean.TRUE.equals(search.getSingle()) && CollUtil.isEmpty(search.getGroupIds()) ?
//                groupChatService.selectOwnerByUserIds(userList)
//                : (null != search.getGroupIds() ? new HashSet<>(search.getGroupIds()) : Collections.emptySet());
        // 获取客户 ID 集合
        Set<Long> customerIds = customerQueryFilter(search, userList);
        if (null != customerIds && customerIds.isEmpty()) {
            // 表示查询不到符合要求的客户
            return Collections.emptyList();
        }
        // 查询时间范围限制了四个月的跨度，因此可无需判定 last 是否存在。 last 表数据量比较大，增加判定，反而影响查询速度。
        MsgInfoQueryBO query = BeanUtil.copyProperties(search, MsgInfoQueryBO.class);
        if (ObjectUtil.equals(Boolean.FALSE, search.getSingle())) {
            query.setGroupIds(CollUtil.isEmpty(search.getGroupIds()) ? groupChatService.selectOwnerByUserIds(userList)
                    : search.getGroupIds());
        } else {
            query.setUserIds(userList);
        }
        query.setSingle(search.getSingle());
        query.setExternal(search.getExternal());
        query.setCustomerIds(customerIds);
        query.setStartTimestamp(search.getStartTime());
        query.setEndTimestamp(search.getEndTime());
        buildMsgFlag(search.getMsgId(), query.getPageUp(), query);
        // choose query method: like or full text
        contentSet(query);
        query.setGroupMsgFlag(filterGroupMsg(search.getFilterGroupMsg()));
        log.info("Msg query global. Operator: {}, query：{},search: {}", UserContext.currentUser().getUserid(), query, search);
        List<MsgInfo> msgInfos = unionGlobalData(query,
                MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()),
                        DateUtils.ms2Date(query.getEndTimestamp())));
        List<GlobalMsgVO> result = new ArrayList<>(msgInfos.size());
        if (CollUtil.isNotEmpty(msgInfos)) {
            Map<Long, EmpInfo> msgUserMap = getMsgUserMapById(new ArrayList<>(msgFilterUserid(msgInfos)));
            Map<Long, CustomerInfo> msgCustomerMap = getMsgCustomerMapById(new ArrayList<>(msgFilterCustomerId(msgInfos)));
            // 时间倒叙返回
            msgInfos.stream().sorted(Comparator.comparing(MsgInfo::getMsgTimestamp).reversed())
                    .forEach(i -> result.add(handleGlobalMsgMap(i, msgUserMap, msgCustomerMap)));
        }
        return result;
    }

    @Override
    public PageWrapVO<GlobalMsgVO> selectGlobalPage(PageWrapVO<MsgGlobalReqVO> pageWrap) {
        if (null == pageWrap || null == pageWrap.getSearch()) {
            return new PageWrapVO<>();
        }
        MsgGlobalReqVO search = pageWrap.getSearch();
        // 设置默认值
//        search.setPageUp(null == search.getPageUp() || search.getPageUp());
        //  数据权限，根据部门集合查询出员工ID合集
        Set<Long> userList = userService.getAndCheckUserIds(search.getScope());
        // 获取客户 ID 集合
        Set<Long> customerIds = customerQueryFilter(search, userList);
        if (null != customerIds && customerIds.isEmpty()) {
            // 表示查询不到符合要求的客户
            return new PageWrapVO<>();
        }
        // 查询时间范围限制了四个月的跨度，因此可无需判定 last 是否存在。 last 表数据量比较大，增加判定，反而影响查询速度。
        MsgInfoQueryBO query = BeanUtil.copyProperties(search, MsgInfoQueryBO.class);
        //  不指定单聊/群聊查询、不指定群的情况下，获取权限范围内的群
        query.setSingle(null == search.getSingle() || search.getSingle());
        query.setUserIds(null);
        query.setGroupIds(null);
        if (ObjectUtil.equals(Boolean.FALSE, query.getSingle())) {
            query.setGroupIds(CollUtil.isEmpty(search.getGroupIds()) ? groupChatService.selectOwnerByUserIds(userList)
                    : search.getGroupIds());
        } else {
            query.setUserIds(userList);
        }
        query.setSingle(search.getSingle());
        query.setExternal(search.getExternal());
        query.setCustomerIds(customerIds);
        query.setStartTimestamp(search.getStartTime());
        query.setEndTimestamp(search.getEndTime());
        // choose query method: like or full text
        contentSet(query);
        query.setGroupMsgFlag(filterGroupMsg(search.getFilterGroupMsg()));
        log.info("Msg query global page. Operator: {}, query：{},search: {}", UserContext.currentUser().getUserid(), query, search);
        List<String> tables = MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()), DateUtils.ms2Date(query.getEndTimestamp()));
        PageWrapVO<GlobalMsgVO> result = new PageWrapVO<>();
        result.setPageSize(pageWrap.getPageSize());
        result.setPageNum(pageWrap.getPageNum());

        List<MsgInfo> msgInfos = unionGlobalPage(query, tables, result);
        if (CollUtil.isNotEmpty(msgInfos)) {
            List<GlobalMsgVO> msgVoList = new ArrayList<>(msgInfos.size());
            Map<Long, EmpInfo> msgUserMap = getMsgUserMapById(new ArrayList<>(msgFilterUserid(msgInfos)));
            Map<Long, CustomerInfo> msgCustomerMap = getMsgCustomerMapById(new ArrayList<>(msgFilterCustomerId(msgInfos)));
            // 时间倒叙返回
            msgInfos.stream().sorted(Comparator.comparing(MsgInfo::getMsgTimestamp).reversed())
                    .forEach(i -> msgVoList.add(handleGlobalMsgMap(i, msgUserMap, msgCustomerMap)));
            result.setList(msgVoList);
        }

        return result;
    }

    @Override
    public void selectGlobalCount(MsgGlobalReqVO search) {
        // 异步执行方法，统计总量
        //  数据权限，根据部门集合查询出员工ID合集
        Set<Long> userList = userService.getAndCheckUserIds(search.getScope());
        // 获取客户 ID 集合
        Set<Long> customerIds = customerQueryFilter(search, userList);
        if (null != customerIds && customerIds.isEmpty()) {
            // 表示查询不到符合要求的客户
            return;
        }
        // 查询时间范围限制了四个月的跨度，因此可无需判定 last 是否存在。 last 表数据量比较大，增加判定，反而影响查询速度。
        MsgInfoQueryBO query = BeanUtil.copyProperties(search, MsgInfoQueryBO.class);
        //  不指定单聊/群聊查询、不指定群的情况下，获取权限范围内的群
        query.setSingle(null == search.getSingle() || search.getSingle());
        query.setUserIds(null);
        query.setGroupIds(null);
        if (ObjectUtil.equals(Boolean.FALSE, query.getSingle())) {
            query.setGroupIds(CollUtil.isEmpty(search.getGroupIds()) ? groupChatService.selectOwnerByUserIds(userList)
                    : search.getGroupIds());
        } else {
            query.setUserIds(userList);
        }
        query.setSingle(search.getSingle());
        query.setExternal(search.getExternal());
        query.setCustomerIds(customerIds);
        query.setStartTimestamp(search.getStartTime());
        query.setEndTimestamp(search.getEndTime());
        // choose query method: like or full text
        contentSet(query);
        query.setGroupMsgFlag(filterGroupMsg(search.getFilterGroupMsg()));
        log.info("Msg query global page. Operator: {}, query：{},search: {}", UserContext.currentUser().getUserid(), query, search);
        List<String> tables = MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()), DateUtils.ms2Date(query.getEndTimestamp()));
        parallelCountTables(query, tables);
    }

    /**
     * 计算页码和返回数据
     */
    public List<MsgInfo> unionGlobalPage(MsgInfoQueryBO query, List<String> tables, PageWrapVO<GlobalMsgVO> page) {
        // 1. 先统计各表总数
        Map<String, Long> tableCounts = parallelCountTables(query, tables);
        // 2. 设置总数
        page.setTotal(tableCounts.values().stream().mapToLong(Long::longValue).sum());
        // 3. 手动分页计算，决定查询哪些表的数据
        return manualPagination(query, tableCounts, page);
    }

    /**
     * 并行统计各表数据量
     */
    public Map<String, Long> parallelCountTables(MsgInfoQueryBO query, List<String> tables) {
        if (CollUtil.isEmpty(tables)) {
            return new HashMap<>();
        }

        long startTime = System.currentTimeMillis();
        log.info("开始并行统计 {} 张表的数据量", tables.size());

        // 创建并行任务 - 修正泛型类型
        List<CompletableFuture<AbstractMap.SimpleEntry<String, Long>>> futures = tables.stream()
                .map(tableName -> MsgSearchPool.supplyAsync(() -> {
                    try {
                        // 克隆查询条件，避免并发修改
                        MsgInfoQueryBO queryClone = BeanUtil.copyProperties(query, MsgInfoQueryBO.class);
                        queryClone.setTableName(tableName);
                        queryClone.setPageSize(null);
                        queryClone.setPageUp(null);
                        queryClone.setLimitStart(null);
                        queryClone.setLimitEnd(null);
                        long count;
                        // 历史数据使用缓存
                        String cacheKey = generateTableCacheKey(tableName, queryClone);
                        String cachedCount = RedisUtils.getStr(cacheKey);
                        if (StrUtil.isNotBlank(cachedCount)) {
                            count = Long.parseLong(cachedCount);
                            log.debug("缓存命中表 {} 统计，数据量: {}", tableName, count);
                        } else {
                            long countStart = System.currentTimeMillis();
                            count = msgInfoService.countGlobalByQuery(queryClone);
                            RedisUtils.addStr(cacheKey, String.valueOf(count), RedisKeyConsts.TimeOut.TEN_MINUTES);
                            log.debug("查询并缓存表 {} 统计，数据量: {}, 耗时: {} 秒", tableName, count, (System.currentTimeMillis() - countStart) / 1000);
                        }
                        return new AbstractMap.SimpleEntry<>(tableName, count);
                    } catch (Exception e) {
                        log.error("统计表 {} 失败", tableName, e);
                        return new AbstractMap.SimpleEntry<>(tableName, 0L);
                    }
                })).collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        Map<String, Long> results = new LinkedHashMap<>();
        try {
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            //  超时
            allFutures.get(120, TimeUnit.SECONDS);
            // 收集结果 - 修正：直接在任务完成后收集
            futures.forEach(future -> {
                try {
                    if (future.isDone() && !future.isCancelled()) {
                        Map.Entry<String, Long> entry = future.get();
                        results.put(entry.getKey(), entry.getValue());
                    }
                } catch (Exception e) {
                    log.error("获取统计结果失败", e);
                }
            });

        } catch (Exception e) {
            log.error("并行统计异常", e);
            // 取消未完成的任务
            futures.forEach(future -> future.cancel(true));
            // 收集已完成的结果
            futures.forEach(future -> {
                try {
                    if (future.isDone() && !future.isCancelled()) {
                        Map.Entry<String, Long> entry = future.get();
                        results.put(entry.getKey(), entry.getValue());
                    }
                } catch (Exception ex) {
                    // 忽略异常，继续收集其他结果
                }
            });
        }

        long totalTime = System.currentTimeMillis() - startTime;
        long totalCount = results.values().stream().mapToLong(Long::longValue).sum();

        log.info("并行统计完成: 成功 {} 张表, 总数据量: {}, 耗时: {} ms", results.size(), totalCount, totalTime);

        return results;
    }

    /**
     * 手动分页计算逻辑
     *
     * @param query       查询条件
     * @param tableCounts 每张表的数据量
     * @param page        全局分页信息
     */
    private List<MsgInfo> manualPagination(MsgInfoQueryBO query, Map<String, Long> tableCounts, PageWrapVO<GlobalMsgVO> page) {
        if (CollUtil.isEmpty(tableCounts) || page.getTotal() == 0) {
            return Collections.emptyList();
        }

        // 1. 计算分页参数
        int pageNum = page.getPageNum() != null ? page.getPageNum() : 1;
        int pageSize = page.getPageSize() != null ? page.getPageSize() : 20;
        long globalStartIndex = (long) (pageNum - 1) * pageSize;  // 全局起始位置(从0开始)
        long globalEndIndex = globalStartIndex + pageSize - 1;    // 全局结束位置

        log.info("Manual pagination: pageNum={}, pageSize={}, globalStartIndex={}, globalEndIndex={}",
                pageNum, pageSize, globalStartIndex, globalEndIndex);

        // 2. 按表查询数据并合并
        List<MsgInfo> result = new ArrayList<>();
        long currentGlobalPosition = 0L; // 当前累计的全局位置

        for (Map.Entry<String, Long> entry : tableCounts.entrySet()) {
            String tableName = entry.getKey();
            long tableRecordCount = entry.getValue();

            // 跳过空表
            if (tableRecordCount == 0) {
                continue;
            }

            // 计算当前表的全局范围: [currentGlobalPosition, currentGlobalPosition + tableRecordCount - 1]
            long tableStartGlobal = currentGlobalPosition;
            long tableEndGlobal = currentGlobalPosition + tableRecordCount - 1;

            log.debug("Table {}: records={}, globalRange=[{},{}]",
                    tableName, tableRecordCount, tableStartGlobal, tableEndGlobal);

            // 如果当前表完全在目标范围之前，跳过
            if (tableEndGlobal < globalStartIndex) {
                currentGlobalPosition += tableRecordCount;
                continue;
            }

            // 如果当前表完全在目标范围之后，或者已经获取足够数据，停止
            if (tableStartGlobal > globalEndIndex || result.size() >= pageSize) {
                break;
            }

            // 计算在当前表中需要查询的范围
            long queryStartGlobal = Math.max(globalStartIndex, tableStartGlobal);
            long queryEndGlobal = Math.min(globalEndIndex, tableEndGlobal);

            // 转换为表内的offset和limit
            int tableOffset = (int) (queryStartGlobal - tableStartGlobal);  // 表内起始位置(从0开始)
            int tableLimit = (int) (queryEndGlobal - queryStartGlobal + 1); // 需要查询的记录数

            // 确保不超过剩余需要的数据量
            int remainingNeeded = pageSize - result.size();
            tableLimit = Math.min(tableLimit, remainingNeeded);

            log.info("Query table {}: offset={}, limit={}, globalRange=[{},{}]",
                    tableName, tableOffset, tableLimit, queryStartGlobal, queryEndGlobal);

            // 设置查询参数并查询
            query.setTableName(tableName);
            query.setLimitStart(tableOffset);
            query.setLimitEnd(tableLimit);

            List<MsgInfo> tableData = msgInfoService.selectGlobalByQuery(query);

            if (CollUtil.isNotEmpty(tableData)) {
                result.addAll(tableData);
                log.debug("Added {} records from table {}, total result size: {}",
                        tableData.size(), tableName, result.size());
            }

            // 更新全局位置
            currentGlobalPosition += tableRecordCount;

            // 如果已经获取足够数据，停止
            if (result.size() >= pageSize) {
                break;
            }
        }

        log.info("Manual pagination completed: requested={}, actual={}", pageSize, result.size());
        return result;
    }

    /**
     * 生成表级缓存键
     */
    private String generateTableCacheKey(String table, MsgInfoQueryBO query) {
        // 创建查询条件的副本，排除分页参数
        MsgInfoQueryBO cacheQuery = BeanUtil.copyProperties(query, MsgInfoQueryBO.class);
        cacheQuery.setPageSize(null);
        cacheQuery.setPageUp(null);
        cacheQuery.setTableName(null);
        cacheQuery.setLimitStart(null);
        cacheQuery.setLimitEnd(null);

        return "msg:global:count:" + table + ":" + DigestUtil.md5Hex16(JacksonUtils.obj2Json(cacheQuery));
    }

    /**
     * 过滤客户:<br/>
     * null：表示不没有客户查询条件。<br/>
     * empty：表示没有符合要求的客户。
     */
    private Set<Long> customerQueryFilter(MsgGlobalReqVO query, Collection<Long> userList) {
        if (null == query) {
            return null;
        }
        if (CollUtil.isEmpty(query.getCodeTzpz()) && CollUtil.isEmpty(query.getCodeFxdj()) && CollUtil.isEmpty(query.getCodeTzqx())
                && CollUtil.isEmpty(query.getCodeFxdjNot()) && CollUtil.isEmpty(query.getCodeTzqxNot()) && CollUtil.isEmpty(query.getCodeTzpzNot())
                && StrUtil.isAllBlank(query.getCustomerName(), query.getYxbzLc(), query.getGtKhid()
                , query.getQxktCyb(), query.getStartDateStr(), query.getEndDateStr())) {
            return null;
        }
        CustomerSearchBO search = BeanUtil.copyProperties(query, CustomerSearchBO.class);
        search.setName(query.getCustomerName());
        search.setUserIds(userList);
        Set<Long> ids = msgLastService.selectCustomerIdsByBO(search);
        return null == ids ? Collections.emptySet() : ids;
    }

    /**
     * 设置全文本查询参数
     */
    private void setFullText(MsgInfoQueryBO query) {
        // 全局检索,排除法，必须包含在正向匹配中。
        // +：包含，-：排除，“”：定义一个短语
        if (StrUtil.isNotBlank(query.getContent())) {
            query.setContent("+\"" + query.getContent() + "\"");
            if (StrUtil.isNotBlank(query.getExcludeContent())) {
                StringJoiner sj = new StringJoiner(" ", " ", "");
                for (String word : query.getExcludeContent().replace(" ", "")
                        .replace("，", ",").split(",")) {
                    sj.add("-\"" + word + "\"");
                }
                query.setExcludeContent(null);
                query.setContent(query.getContent() + sj);
            }
        } else {
            // 如果没有正向匹配词,则反向匹配只能采取 not match
            if (StrUtil.isNotBlank(query.getExcludeContent())) {
                StringJoiner sj = new StringJoiner(" ");
                for (String word : query.getExcludeContent().replace(" ", "")
                        .replace("，", ",").split(",")) {
                    sj.add("+\"" + word + "\"");
                }
                // 全文本索引，空格表示多词拆分
                query.setExcludeContent(sj.toString());
            }
        }
    }

    private List<MsgInfo> unionGlobalData(MsgInfoQueryBO query, List<String> allTables) {
        int total = query.getPageSize();
        List<MsgInfo> result = new ArrayList<>(total);
        // allTables 根据时间倒叙。msg_info_202210
        // 上翻页的时候，需要调整为根据时间正序，倒叙查询是以截止时间判断的。
        allTables = query.getPageUp()
                ? allTables.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList())
                : allTables.stream().sorted().collect(Collectors.toList());
        for (String table : allTables) {
            query.setTableName(table);
            query.setPageSize(total - result.size());
            List<MsgInfo> msgInfos = msgInfoService.selectGlobalByQuery(query);
            result.addAll(msgInfos);
            if (result.size() >= total) {
                break;
            }
        }
        return result;
    }

    @Override
    public List<MessageVO> selectPosition(MsgPositionVO search) {
        MsgInfo msgInfo = msgInfoService.getByMsgId(search.getMsgId());
        if (null == msgInfo) {
            return Collections.emptyList();
        }
        MsgLast last = msgLastService.getByRelation(msgInfo.getRelation());
        if (null == last) {
            log.info("Message global search msg last is null.");
            //  找不到聊天记录，直接返回
            return Collections.emptyList();
        }
        List<MessageVO> result = new ArrayList<>(search.getPageSize() * 2 + 1);
        MsgInfoQueryBO query = new MsgInfoQueryBO();
        query.setRelation(msgInfo.getRelation());
        query.setPageSize(search.getPageSize());
        query.setPageUp(search.getPageUp());
        List<MsgInfo> data = new ArrayList<>(search.getPageSize() * 2 + 1);
        if (null == query.getPageUp()) {
            // 取上下数据的时候，把原有数据包含在里面，最后进行排序。
            data.add(msgInfo);
            // 过去数据
            query.setPageUp(true);
            query.setStartTimestamp(DateUtils.date2Milli(last.getCreateDatetime()));
            query.setEndTimestamp(msgInfo.getMsgTimestamp() - 1L);
            data.addAll(unionData(query, MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp())
                    , DateUtils.ms2Date(query.getEndTimestamp()))));
            //  未来数据
            query.setPageUp(false);
            query.setStartTimestamp(msgInfo.getMsgTimestamp() + 1L);
            query.setEndTimestamp(last.getMsgTimestamp());
            data.addAll(unionData(query, MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()),
                    DateUtils.ms2Date(query.getEndTimestamp()))));
        } else if (query.getPageUp()) {
            // 过去的数据 - 不包含消息本身
            query.setStartTimestamp(DateUtils.date2Milli(last.getCreateDatetime()));
            query.setEndTimestamp(msgInfo.getMsgTimestamp() - 1L);
            data.addAll(unionData(query, MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()),
                    DateUtils.ms2Date(query.getEndTimestamp()))));
        } else {
            // 未来的数据 - 不包含消息本身
            query.setStartTimestamp(msgInfo.getMsgTimestamp() + 1L);
            query.setEndTimestamp(last.getMsgTimestamp());
            data.addAll(unionData(query, MsgUtils.getAllTable(DateUtils.ms2Date(query.getStartTimestamp()),
                    DateUtils.ms2Date(query.getEndTimestamp()))));
        }
        // 排序，按时间倒叙输出
        data.stream().sorted(Comparator.comparing(MsgInfo::getMsgTimestamp))
                .forEach(i -> result.add(msgInfo2MsgVO(i)));
        return result;
    }

    @Override
    public GlobalMsgVO getByMsgId(String msgId) {
        MsgInfo msgInfo = msgInfoService.getByMsgId(msgId);
        Set<Long> userIds = msgFilterUserid(Collections.singletonList(msgInfo));
        Set<Long> customerIds = msgFilterCustomerId(Collections.singletonList(msgInfo));
        Map<Long, EmpInfo> msgUserMap = getMsgUserMapById(new ArrayList<>(userIds));
        Map<Long, CustomerInfo> msgCustomerMap = getMsgCustomerMapById(new ArrayList<>(customerIds));
        return handleGlobalMsgMap(msgInfo, msgUserMap, msgCustomerMap);
    }

    /**
     * 获取员工id和具体信息的Map<Id,EmpInfo>
     *
     * @param userIds 员工id
     * @return 结果
     */
    private Map<Long, EmpInfo> getMsgUserMapById(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        Map<Long, EmpInfo> userMap = new HashMap<>(userIds.size());
        // 通过员工id集合查询员工
        List<EmpInfo> empInfos = userService.selectEmpInfoByIds(userIds);
        // 处理员工为员工userid和员工信息的Map<userid,EmpInfo>
        Map<Long, List<EmpInfo>> empInfoUseridMap = empInfos.stream().collect(Collectors.groupingBy(EmpInfo::getId));
        if (ObjectUtil.isEmpty(empInfoUseridMap)) {
            return userMap;
        }
        empInfoUseridMap.forEach((key, value) -> userMap.put(key, value.get(0)));
        return userMap;
    }

    /**
     * 获取客户id和具体信息的Map<Id,CustomerInfo>
     *
     * @param customerIds 客户id
     * @return 结果
     */
    private Map<Long, CustomerInfo> getMsgCustomerMapById(List<Long> customerIds) {
        if (CollUtil.isEmpty(customerIds)) {
            return Collections.emptyMap();
        }
        // 通过客户id集合查询客户
        List<CustomerInfo> customerInfos = customerService.selectByIds(customerIds);
        // 处理员工为客户的externalUserid和客户信息的Map<externalUserid,CustomerInfo>
        return customerInfos.stream().collect(Collectors
                .toMap(CustomerInfo::getId, info -> info, (old, newVal) -> old));
    }

    /**
     * 处理消息内容
     *
     * @param msgInfo info中的具体内容
     * @return 结果
     */
    private MessageVO msgInfo2MsgVO(MsgInfo msgInfo) {
        if (null == msgInfo) {
            return new MessageVO();
        }
        MessageVO messageVO = BeanUtil.copyProperties(msgInfo, MessageVO.class);
        // 发送时间、发送人信息
        messageVO.setMsgDateTime(DateUtils.ms2Time(msgInfo.getMsgTimestamp()));
        handleFrom(messageVO, msgInfo.getFromId());
        // 消息类型
        MessageEnum messageEnum = MessageEnum.valueOf(msgInfo.getMsgType());
        messageVO.setMsgTypeStr(messageEnum.getType());
        // 无需再对 Content 进行解析，只需要针对媒体信息进行补充即可
        // 音频转写成功的，会回写内容到 Content 因此无需额外处理。
        Optional.ofNullable(strategyContext.getMediaStrategy(messageEnum.getType())).ifPresent(i -> {
            messageVO.setMd5Sum(i.getFileMd5(msgInfo.getChatData()));
            Optional.ofNullable(msgFileService.getByMd5Sum(messageVO.getMd5Sum())).ifPresent(f -> {
                messageVO.setDownloadUrl(fileInfoService.getFileUrl(f));
                messageVO.setDownload(StrUtil.isNotBlank(messageVO.getDownloadUrl()));
                messageVO.setVoiceIsWav(FileConsts.AMR_2_WAV.equals(f.getFileType()));
            });
        });
        Strategy strategy = strategyContext.getStrategy(messageEnum.getType());
        // 处理内容
        if (strategy == null) {
            return messageVO;
        }
        // 转换对象，补充消息属性
        return strategy.handleContent(msgInfo.getChatData(), messageVO);
    }

    /**
     * 处理消息内容
     *
     * @param msgInfo        info中的具体内容
     * @param msgUserMap     处理员工为员工userid和员工信息的Map<userid,EmpInfo>
     * @param msgCustomerMap 处理员工为客户的externalUserid和客户信息的Map<externalUserid,CustomerInfo>
     * @return 结果
     */
    private GlobalMsgVO handleGlobalMsgMap(MsgInfo msgInfo, Map<Long, EmpInfo> msgUserMap, Map<Long, CustomerInfo> msgCustomerMap) {
        GlobalMsgVO sendVO = BeanUtil.copyProperties(msgInfo, GlobalMsgVO.class);
        // 消息发送时间,发送人信息,接收人信息
        sendVO.setMsgDateTime(DateUtils.ms2Time(msgInfo.getMsgTimestamp()));
        handleGlobalFrom(sendVO, msgInfo.getFromId(), msgUserMap, msgCustomerMap);
        handleGlobalReceive(sendVO, msgInfo.getReceiveId(), msgUserMap, msgCustomerMap);
        // 消息类型
        MessageEnum messageEnum = sendVO.getMsgTypeEnum();
        sendVO.setMsgTypeStr(messageEnum.getType());
        sendVO.setTypeAlias(messageEnum.getExplain());
        // 无值的情况下，先设置 Explain()
        sendVO.setContent(StrUtil.isBlank(sendVO.getContent()) ? messageEnum.getExplain() : sendVO.getContent());
        // 处理内容
        Strategy strategy = strategyContext.getStrategy(messageEnum.getType());
        if (strategy == null) {
            return sendVO;
        }
        // 全局信息 无需再对 Content 进行解析，只需要针对媒体信息进行补充即可
        // 音频转写成功的，会回写内容到 Content 因此无需额外处理。
        Optional.ofNullable(strategyContext.getMediaStrategy(messageEnum.getType())).ifPresent(i -> {
            sendVO.setMd5Sum(i.getFileMd5(msgInfo.getChatData()));
            Optional.ofNullable(msgFileService.getByMd5Sum(sendVO.getMd5Sum())).ifPresent(f -> {
                sendVO.setDownloadUrl(fileInfoService.getFileUrl(f));
                sendVO.setDownload(StrUtil.isNotBlank(sendVO.getDownloadUrl()));
                sendVO.setVoiceIsWav(FileConsts.AMR_2_WAV.equals(f.getFileType()));
            });
        });
        return sendVO;
    }

    /**
     * 获取发送人
     *
     * @param messageVO 消息
     */
    private void handleFrom(MessageVO messageVO, Long from) {
        SendTypeEnum sendTypeEnum = messageVO.getSendTypeEnum();
        messageVO.setFromType(sendTypeEnum.getFromType());
        switch (sendTypeEnum) {
            // 发送人为员工
            case USER_TO_CUSTOMER:
            case USER_TO_GROUP:
            case USER_TO_USER:
                EmpInfo user = userService.selectEmpInfoById(from);
                if (user == null) {
                    break;
                }
                messageVO.setFromInfo(user2People(user));
                break;
            // 发送人为客户
            case CUSTOMER_TO_GROUP:
            case CUSTOMER_TO_USER:
                Customer customer = customerService.selectOneById(from);
                if (customer == null) {
                    break;
                }
                messageVO.setFromInfo(customer2People(customer));
                break;
            default:
                break;
        }
    }

    /**
     * 获取全局的发送人
     *
     * @param messageVO      消息
     * @param msgUserMap     处理员工为员工userid和员工信息的Map<userid,EmpInfo>
     * @param msgCustomerMap 处理员工为客户的externalUserid和客户信息的Map<externalUserid,CustomerInfo>
     */
    private void handleGlobalFrom(GlobalMsgVO messageVO, Long fromId, Map<Long, EmpInfo> msgUserMap, Map<Long,
            CustomerInfo> msgCustomerMap) {
        SendTypeEnum sendTypeEnum = messageVO.getSendTypeEnum();
        messageVO.setFromType(sendTypeEnum.getFromType());
        messageVO.setReceiveType(sendTypeEnum.getReceiveType());
        switch (sendTypeEnum) {
            // 发送人为员工
            case USER_TO_CUSTOMER:
            case USER_TO_USER:
            case USER_TO_GROUP:
                if (msgUserMap.containsKey(fromId)) {
                    messageVO.setFromInfo(user2People(msgUserMap.get(fromId)));
                }
                break;
            // 发送人为客户
            case CUSTOMER_TO_USER:
            case CUSTOMER_TO_GROUP:
                if (msgCustomerMap.containsKey(fromId)) {
                    messageVO.setFromInfo(customer2People(msgCustomerMap.get(fromId)));
                }
                break;
            default:
                break;
        }
    }

    /**
     * 获取全局的接收人
     *
     * @param messageVO      消息
     * @param msgUserMap     处理员工为员工userid和员工信息的Map<userid,EmpInfo>
     * @param msgCustomerMap 处理员工为客户的externalUserid和客户信息的Map<externalUserid,CustomerInfo>
     */
    private void handleGlobalReceive(GlobalMsgVO messageVO, Long receiveId, Map<Long, EmpInfo> msgUserMap, Map<Long,
            CustomerInfo> msgCustomerMap) {
        SendTypeEnum sendTypeEnum = messageVO.getSendTypeEnum();
        switch (sendTypeEnum) {
            // 接收人为员工
            case CUSTOMER_TO_USER:
            case USER_TO_USER:
                if (msgUserMap.containsKey(receiveId)) {
                    messageVO.setReceiveInfo(user2People(msgUserMap.get(receiveId)));
                }
                break;
            // 接收人为客户
            case USER_TO_CUSTOMER:
                if (msgCustomerMap.containsKey(receiveId)) {
                    messageVO.setReceiveInfo(customer2People(msgCustomerMap.get(receiveId)));
                }
                break;
            case USER_TO_GROUP:
            case CUSTOMER_TO_GROUP:
            case NOT_FUND_TO_GROUP:
                GroupChat oneByChatId = groupChatService.selectOneById(receiveId);
                if (oneByChatId != null) {
                    messageVO.setReceiveInfo(chat2People(oneByChatId));
                }
                break;
            default:
                break;
        }
    }

    private MsgPeopleVO user2People(EmpInfo empInfo) {
        MsgPeopleVO people = BeanUtil.copyProperties(empInfo, MsgPeopleVO.class);
        people.setUserId(empInfo.getUserId());
        return people;
    }

    private MsgPeopleVO customer2People(CustomerInfo customerInfo) {
        MsgPeopleVO people = BeanUtil.copyProperties(customerInfo, MsgPeopleVO.class);
        people.setExternalUserid(customerInfo.getExternalUserid());
        people.setCustomerType(customerInfo.getType());
        return people;
    }

    private MsgPeopleVO customer2People(Customer customer) {
        MsgPeopleVO people = BeanUtil.copyProperties(customer, MsgPeopleVO.class);
        people.setExternalUserid(customer.getExternalUserid());
        people.setCustomerType(customer.getType());
        return people;
    }

    private MsgPeopleVO chat2People(GroupChat groupChat) {
        MsgPeopleVO people = BeanUtil.copyProperties(groupChat, MsgPeopleVO.class);
        people.setChatId(groupChat.getChatId());
        return people;
    }

    private Set<Long> msgFilterUserid(List<MsgInfo> msgInfos) {
        Set<Long> result = new HashSet<>(msgInfos.size());
        msgInfos.forEach(i -> {
            switch (SendTypeEnum.valueOf(i.getSendType())) {
                // 发送人为员工
                case USER_TO_CUSTOMER:
                case USER_TO_GROUP:
                    result.add(i.getFromId());
                    break;
                case USER_TO_USER:
                    result.add(i.getFromId());
                    result.add(i.getReceiveId());
                    break;
                case CUSTOMER_TO_USER:
                    result.add(i.getReceiveId());
                    break;
                default:
                    break;
            }
        });
        return result;
    }

    private Set<Long> msgFilterCustomerId(List<MsgInfo> msgInfos) {
        return msgInfos.stream().map(i -> {
            switch (SendTypeEnum.valueOf(i.getSendType())) {
                // 发送人为员工
                case USER_TO_CUSTOMER:
                    return i.getReceiveId();
                // 发送人为客户
                case CUSTOMER_TO_USER:
                case CUSTOMER_TO_GROUP:
                    return i.getFromId();
                default:
                    break;
            }
            return 0L;
        }).collect(Collectors.toSet());
    }

    /**
     * 选择查询方法：全文本还是like
     */
    private void contentSet(MsgInfoQueryBO query) {
        String like = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SYSTEM, "query_method_like");
        if ("true".equalsIgnoreCase(like)) {
            query.setLikeContent(true);
        }
        // 所有索引条件都没用到，则走全文本索引
        if (CollUtil.isEmpty(query.getUserIds()) && CollUtil.isEmpty(query.getGroupIds()) && CollUtil.isEmpty(query.getCustomerIds())
                && null == query.getStartTimestamp() && null == query.getEndTimestamp()
                && StrUtil.isBlank(query.getMsgId()) && StrUtil.isBlank(query.getRelation())) {
            query.setLikeContent(false);
        }
        // 不走 like 查询，则调整全文本信息
        if (!query.isLikeContent()) {
            setFullText(query);
        } else {
            if (StrUtil.isNotBlank(query.getExcludeContent())) {
                query.setExcludeContentList(Arrays.asList(query.getExcludeContent().replace(" ", "")
                        .replace("，", ",").split(",")));
            }
        }
    }

    /**
     * 过滤群发消息
     */
    private String filterGroupMsg(Boolean filter) {
        // 这个配置没法从数据库读取，执行不进去，除非用程序刷sql 进去，数据库管理工具没法显示
        // 国信消息，群发标识：\u200B，会在群发消息里，增加这个开头
        // 测试方式，从 Idea 中复制内容到微信发送，示例：\u200B包含空白字符
        return Boolean.TRUE.equals(filter) ? "\u200B" : null;
    }

}
