package com.guosen.ewas.wechat.score.domain.vo;

public enum MsgFlagTypeEnum {
    /**
     * 优秀话术
     */
    GOOD(1),
    /**
     * 问题话术
     */
    QUESTIONS(2),
    /**
     * 扣分
     */
    SUBTRACTION(3);
    /**
     * 对应数据库的
     */
    private final Integer type;

    MsgFlagTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return this.type;
    }

    public static MsgFlagTypeEnum value(Integer type) {
        for (MsgFlagTypeEnum value : values()) {
            if (type.equals(value.getType())) {
                return value;
            }
        }
        return null;
    }
}
