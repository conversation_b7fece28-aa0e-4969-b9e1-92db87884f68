package com.guosen.ewas.wechat.common.service;

import com.guosen.ewas.wechat.common.domain.po.ConfigKv;

import java.util.List;

/**
 * the service interface of ConfigKv
 *
 * <AUTHOR>
 * @date 2021-09-03 19:29:16.0743
 */
public interface ConfigKvService {

    /**
     * 逻辑删除数据，根据主键
     *
     * @param id 主键
     * @return int
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 根据主键获取数据
     */
    ConfigKv selectByPrimaryKey(Integer id);

    /**
     * 根据类型获取数据
     *
     * @param type 分类
     */
    List<ConfigKv> selectByType(String type);

    /**
     * 根据type和key获取数据
     *
     * @param type 分类
     * @param key  键值
     */
    ConfigKv getByTypeAndKey(String type, String key);

    /**
     * 根据type和key获取value
     *
     * @param type 分类
     * @param key  键值
     */
    String getValueByTypeAndKey(String type, String key);

    /**
     * 根据type和key获取value,没有就走默认值，优先取缓存
     *
     * @param type 分类
     * @param key  键值
     */
    String getCacheValueByTypeAndKey(String type, String key, String defVal);

    /**
     * 更新数据
     */
    int updateByPrimaryKeySelective(ConfigKv record);

    /**
     * 新增
     */
    int insertSelective(ConfigKv record);

    /**
     * 根据分类和键值删除数据
     *
     * @param type 分类
     * @param key  键值
     */
    int deletePhysicalByTypeAndKey(String type, String key);

}