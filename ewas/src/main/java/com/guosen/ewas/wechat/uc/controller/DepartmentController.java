package com.guosen.ewas.wechat.uc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.guosen.commonsrv.zebra.anotation.ZebraController;
import com.guosen.commonsrv.zebra.anotation.ZebraMapping;
import com.guosen.commonsrv.zebra.anotation.ZebraParam;
import com.guosen.ewas.wechat.uc.dao.DepartmentMapper;
import com.guosen.ewas.wechat.uc.dao.UserMapper;
import com.guosen.ewas.wechat.uc.domain.vo.DepartmentUserTreeVO;
import com.guosen.ewas.wechat.uc.service.DepartmentService;
import com.guosen.jxhcommon.contracts.ResponseContract;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZebraController
public class DepartmentController {
    private final DepartmentService departmentService;
    private final DepartmentMapper departmentMapper;
    private final UserMapper userMapper;

    public DepartmentController(DepartmentService departmentService, DepartmentMapper departmentMapper, UserMapper userMapper) {
        this.departmentService = departmentService;
        this.departmentMapper = departmentMapper;
        this.userMapper = userMapper;
    }

    /**
     * 部门树
     *
     * @return 部门树
     */
    @ZebraMapping("department/tree")
    public ResponseContract<JSONArray> tree(@ZebraParam(name = "all", defaultValue = "0") Integer isAll) {
        List<DepartmentUserTreeVO> treeResult = departmentService.departmentScope(isAll);
        return ResponseContract.success(JSONArray.parseArray(JSON.toJSONString(treeResult)));
    }

    @ZebraMapping("department/cacheversion")
    public ResponseContract<String> cacheVersion() {
        return ResponseContract.success(departmentService.cacheVersion());
    }


    /**
     * 根据名字 搜索员工或者部门
     *
     * @param name      部门或员工的名字
     * @return 搜索结果
     */
    @ZebraMapping("department/tree/search")
    public ResponseContract<JSONArray> search(@ZebraParam("name") String name) {
        List<DepartmentUserTreeVO> voList = new LinkedList<>();
        departmentMapper.selectAllByNameAndIdIn(name, null).forEach(d -> {
            DepartmentUserTreeVO data = new DepartmentUserTreeVO();
            data.setId(d.getId());
            data.setName(d.getName());
            data.setParentId(d.getParentId());
            data.setSort(d.getSort());
            data.setType(2);
            voList.add(data);
        });
        userMapper.selectAllByNameAndIdIn(name, null).forEach(u -> {
            DepartmentUserTreeVO data = new DepartmentUserTreeVO();
            data.setId(u.getId());
            data.setName(u.getName());
            data.setState(u.getState());
            data.setUserid(u.getUserid());
            data.setType(1);
            voList.add(data);
        });
        return ResponseContract.success(JSONArray.parseArray(JSON.toJSONString(voList)));
    }
}
