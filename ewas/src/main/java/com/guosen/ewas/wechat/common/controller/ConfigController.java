package com.guosen.ewas.wechat.common.controller;


import cn.hutool.core.util.StrUtil;
import com.guosen.commonsrv.zebra.anotation.ZebraController;
import com.guosen.commonsrv.zebra.anotation.ZebraMapping;
import com.guosen.ewas.wechat.common.constant.ConfigConsts;
import com.guosen.ewas.wechat.common.service.ConfigKvService;
import com.guosen.jxhcommon.contracts.ResponseContract;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 配置信息相关控制类
 *
 * <AUTHOR>
 */
@Slf4j
@ZebraController
@RequiredArgsConstructor
public class ConfigController {
    private final ConfigKvService configKvService;

    /**
     * 消息可以查询的时间跨度
     */
    @ZebraMapping("config/queryMonth")
    public ResponseContract<Integer> queryMonth() {
        String value = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SETTING, ConfigConsts.Key.MSG_QUERY_MONTH);
        return ResponseContract.success(StrUtil.isBlank(value) ? 2 : Integer.parseInt(value));
    }


    @ZebraMapping("config/toPdfTypes")
    public ResponseContract<String> toPdfTypes() {
        String value = configKvService.getValueByTypeAndKey(ConfigConsts.Type.SETTING, ConfigConsts.Key.TO_PDF_TYPES);
        return ResponseContract.success(StrUtil.isBlank(value) ? ".doc,.docx.,.pdf,.pptx,.ppt,.xls,.xlsx,.txt" : value);
    }

}
