package com.guosen.ewas.wechat.uc.service;

import org.springframework.context.ApplicationEvent;

/**
 * 刷新部门树
 * user变更  部门变更  权限变更
 *
 * <AUTHOR>
 */
public class DeptTreeRefreshEvent extends ApplicationEvent {
    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public DeptTreeRefreshEvent(Object source) {
        super(source);
    }
}
