package com.guosen.ewas.wechat.common.service;

import com.guosen.ewas.wechat.common.domain.entity.FileInfoHead;

import java.io.File;

/**
 * 文件业务
 *
 * <AUTHOR>
 * @date 2021/10/18 9:55
 */
public interface IFileInfoHeadService {

    /**
     * 上传企业头像到服务器
     *
     * @param userid   可能是客户的external_userid也可能是员工的userid
     * @param qyAvatar 头像地址
     * @return 文件下载地址
     */
    String uploadAvatar(String qyAvatar, String userid);

    /**
     * 获取文件
     *
     * @param path 相对路径
     * @return 文件资源对象
     */
    File getFile(String path);

    /**
     * 获取文件对象
     *
     * @param uid 文件UId
     * @return FileInfoHead
     */
    FileInfoHead getFileInfoHeadByUid(String uid);
}
