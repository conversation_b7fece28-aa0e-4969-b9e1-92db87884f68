syntax = "proto3";

option java_package = "com.guosen.ewas.wechat.model";
option java_outer_classname = "EwasModelProto";
package com.guosen.ewas.wechat.model;
import "zebra/common_dto.proto";

message ApiRequest {
    string functionCode = 1;
    string notesid = 2;
    string jgid = 3;
    string brhid = 4;
    map<string,string> params = 5;
}

message ApiResponse {
    repeated com.guosen.zebra.dto.ResultDTO result =1;
    string repeatJson = 2;
}